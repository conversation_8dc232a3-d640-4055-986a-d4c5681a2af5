# 灭火指挥调度地图 Agent — 后端项目架构设计

## 项目概述
基于 LangGraph 最新的 Supervisor 模式，设计多 Agent 协作的消防救援智能调度系统。采用分层架构，支持事件解析、空间检索、调度规划、路径评估等核心功能。

## 技术栈
- **核心框架**: LangGraph 0.6.0+ (Supervisor 模式)
- **数据库**: PostgreSQL + 应用层几何计算
- **缓存**: Redis (热点数据、会话状态)
- **API**: FastAPI + WebSocket (实时推送)
- **地图服务**: 高德地图 API
- **部署**: Docker + LangGraph Cloud

## 项目目录结构

```
fire-dispatch-backend/
├── fire_dispatch_agent/                    # 主应用包
│   ├── __init__.py
│   ├── main.py                            # FastAPI 应用入口
│   ├── supervisor.py                      # 主 Supervisor Agent
│   │
│   ├── agents/                            # 专业 Agent 模块
│   │   ├── __init__.py
│   │   ├── event_parser.py               # 事件解析 Agent
│   │   ├── spatial_analyzer.py           # 空间分析 Agent  
│   │   ├── dispatch_planner.py           # 调度规划 Agent
│   │   ├── route_evaluator.py            # 路径评估 Agent
│   │   └── notification_agent.py         # 通知推送 Agent
│   │
│   ├── utils/                            # 工具模块
│   │   ├── __init__.py
│   │   ├── tools.py                      # LangGraph 工具定义
│   │   ├── nodes.py                      # 节点函数
│   │   ├── state.py                      # 状态定义
│   │   ├── database.py                   # 数据库连接与查询
│   │   ├── cache.py                      # Redis 缓存操作
│   │   ├── amap_client.py                # 高德地图 API 客户端
│   │   └── geo_utils.py                  # 几何计算工具
│   │
│   ├── models/                           # 数据模型
│   │   ├── __init__.py
│   │   ├── event.py                      # 事件模型
│   │   ├── resource.py                   # 资源模型
│   │   ├── dispatch.py                   # 调度方案模型
│   │   └── response.py                   # API 响应模型
│   │
│   ├── api/                              # API 路由
│   │   ├── __init__.py
│   │   ├── dispatch.py                   # 调度接口
│   │   ├── resources.py                  # 资源查询接口
│   │   ├── websocket.py                  # WebSocket 处理
│   │   └── health.py                     # 健康检查
│   │
│   ├── config/                           # 配置管理
│   │   ├── __init__.py
│   │   ├── settings.py                   # 应用配置
│   │   ├── database.py                   # 数据库配置
│   │   └── logging.py                    # 日志配置
│   │
│   └── tests/                            # 测试模块
│       ├── __init__.py
│       ├── test_agents.py                # Agent 测试
│       ├── test_tools.py                 # 工具测试
│       ├── test_api.py                   # API 测试
│       └── fixtures/                     # 测试数据
│
├── scripts/                              # 脚本工具
│   ├── db_inspector.py                   # 数据库结构检查
│   ├── data_migration.py                 # 数据迁移
│   └── performance_test.py               # 性能测试
│
├── docs/                                 # 文档
│   ├── 数据字典与实体映射.md
│   ├── API文档.md
│   └── 部署指南.md
│
├── .env                                  # 环境变量
├── .env.example                          # 环境变量示例
├── pyproject.toml                        # 项目依赖
├── langgraph.json                        # LangGraph 配置
├── docker-compose.yml                    # Docker 编排
├── Dockerfile                            # Docker 镜像
└── README.md                             # 项目说明
```

## 核心架构设计

### 1. Supervisor 架构模式

```python
# 主 Supervisor 负责协调各专业 Agent
fire_dispatch_supervisor = create_supervisor(
    agents=[
        event_parser_agent,      # 事件解析
        spatial_analyzer_agent,  # 空间分析  
        dispatch_planner_agent,  # 调度规划
        route_evaluator_agent,   # 路径评估
        notification_agent       # 通知推送
    ],
    model=llm,
    prompt="你是消防救援调度中心的智能指挥官，负责协调各专业模块完成应急调度任务。"
)
```

### 2. 多层级 Agent 协作

```
Main Supervisor (主调度员)
├── Event Parser Agent (事件解析专家)
├── Spatial Analysis Team (空间分析团队)
│   ├── Jurisdiction Agent (辖区判断)
│   ├── Resource Locator Agent (资源定位)
│   └── Coverage Analyzer Agent (覆盖分析)
├── Dispatch Planning Team (调度规划团队)  
│   ├── Resource Selector Agent (资源选择)
│   ├── Priority Ranker Agent (优先级排序)
│   └── Backup Planner Agent (备选方案)
├── Route Evaluation Agent (路径评估专家)
└── Notification Agent (通知推送专家)
```

### 3. 状态管理

```python
@dataclass
class FireDispatchState(MessagesState):
    # 事件信息
    event: Optional[EventContext] = None
    
    # 空间分析结果
    jurisdiction: Optional[str] = None
    nearby_stations: List[ResourceUnit] = field(default_factory=list)
    nearby_hydrants: List[ResourceUnit] = field(default_factory=list)
    
    # 调度方案
    dispatch_plans: List[DispatchPlan] = field(default_factory=list)
    selected_plan: Optional[DispatchPlan] = None
    
    # 路径信息
    routes: List[RouteInfo] = field(default_factory=list)
    
    # 执行状态
    execution_status: str = "pending"
    errors: List[str] = field(default_factory=list)
```

## 关键组件设计

### 1. 数据库工具层 (`utils/database.py`)

```python
class DatabaseTools:
    @tool
    async def query_nearby_stations(self, lng: float, lat: float, radius: float = 10):
        """查询附近救援站"""
        
    @tool  
    async def query_nearby_hydrants(self, lng: float, lat: float, radius: float = 5):
        """查询附近消火栓"""
        
    @tool
    async def determine_jurisdiction(self, lng: float, lat: float):
        """判断事件辖区"""
        
    @tool
    async def get_resource_status(self, resource_ids: List[str]):
        """获取资源状态"""
```

### 2. 高德地图工具层 (`utils/amap_client.py`)

```python
class AmapTools:
    @tool
    async def geocode_address(self, address: str):
        """地址地理编码"""
        
    @tool
    async def calculate_route(self, start: Tuple[float, float], end: Tuple[float, float]):
        """计算路径与ETA"""
        
    @tool
    async def get_traffic_status(self, route_points: List[Tuple[float, float]]):
        """获取路况信息"""
```

### 3. 缓存工具层 (`utils/cache.py`)

```python
class CacheTools:
    @tool
    async def get_cached_eta(self, start: str, end: str):
        """获取缓存的ETA"""
        
    @tool
    async def cache_dispatch_plan(self, plan_id: str, plan: DispatchPlan):
        """缓存调度方案"""
```

## API 设计

### 1. 调度接口 (`api/dispatch.py`)

```python
@router.post("/dispatch/emergency")
async def create_emergency_dispatch(event: EmergencyEvent):
    """创建应急调度"""
    
@router.get("/dispatch/{dispatch_id}")
async def get_dispatch_status(dispatch_id: str):
    """获取调度状态"""
    
@router.post("/dispatch/{dispatch_id}/execute")
async def execute_dispatch_plan(dispatch_id: str):
    """执行调度方案"""
```

### 2. WebSocket 实时推送 (`api/websocket.py`)

```python
@router.websocket("/ws/dispatch/{client_id}")
async def dispatch_websocket(websocket: WebSocket, client_id: str):
    """调度状态实时推送"""
```

## 配置管理

### 1. LangGraph 配置 (`langgraph.json`)

```json
{
  "dependencies": [".", "langchain-openai", "langgraph-supervisor"],
  "graphs": {
    "fire_dispatch_supervisor": "./fire_dispatch_agent/supervisor.py:supervisor_graph"
  },
  "env": "./.env"
}
```

### 2. 应用配置 (`config/settings.py`)

```python
class Settings(BaseSettings):
    # 数据库配置
    DATABASE_URL: str
    REDIS_URL: str
    
    # 高德地图配置
    AMAP_API_KEY: str
    AMAP_BASE_URL: str = "https://restapi.amap.com"
    
    # LLM 配置
    OPENAI_API_KEY: str
    MODEL_NAME: str = "gpt-4o"
    
    # 业务配置
    DEFAULT_SEARCH_RADIUS: float = 10.0
    MAX_DISPATCH_PLANS: int = 3
    ETA_CACHE_TTL: int = 300
```

## 部署配置

### 1. Docker 配置 (`Dockerfile`)

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY pyproject.toml ./
RUN pip install -e .

COPY . .
EXPOSE 8000

CMD ["uvicorn", "fire_dispatch_agent.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 2. Docker Compose (`docker-compose.yml`)

```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=******************************/fire_dispatch
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
      
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: fire_dispatch
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
      
  redis:
    image: redis:7-alpine
    
volumes:
  postgres_data:
```

## 开发与测试

### 1. 依赖管理 (`pyproject.toml`)

```toml
[project]
name = "fire-dispatch-agent"
version = "0.1.0"
dependencies = [
    "langgraph>=0.6.0",
    "langgraph-supervisor>=0.1.0", 
    "langchain-openai>=0.2.0",
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "psycopg2-binary>=2.9.0",
    "redis>=5.0.0",
    "pydantic>=2.5.0",
    "httpx>=0.25.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "ruff>=0.1.0"
]
```

### 2. 测试策略

- **单元测试**: 各 Agent 和工具函数
- **集成测试**: Supervisor 协作流程
- **API 测试**: FastAPI 接口
- **性能测试**: 并发调度能力

## 下一步实施计划

1. **阶段1**: 搭建基础项目结构和配置
2. **阶段2**: 实现核心 Agent 和工具
3. **阶段3**: 集成 Supervisor 协作机制
4. **阶段4**: 开发 API 和 WebSocket 接口
5. **阶段5**: 测试、优化和部署

这个架构设计充分利用了 LangGraph 最新的 Supervisor 模式，实现了模块化、可扩展的多 Agent 协作系统，为消防救援调度提供了智能化的技术支撑。
