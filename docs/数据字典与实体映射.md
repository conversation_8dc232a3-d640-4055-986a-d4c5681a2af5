# 数据字典与实体映射分析

## 概述
基于 `report_map` 模式的数据库结构检查，发现21个表，涵盖救援站、消防工作站、辖区边界、重点单位、派出所等核心实体。数据库使用 PostgreSQL 10.7，**未安装PostGIS**，坐标以经纬度数值字段存储。

## 核心实体映射

### 1. 救援站相关 (消防救援站)

#### `jyz_coordinate` - 救援站坐标表 (76行)
- **业务含义**: 消防救援站的基础位置信息
- **关键字段**:
  - `dadui`: 所属大队 (如"西岗大队", "战勤保障")
  - `jyz`: 救援站名称 (如"华东路战勤保障站", "北京街消防救援站")
  - `horizontal`: 经度 (字符串格式, 如"121.595036")
  - `vertical`: 纬度 (字符串格式, 如"39.005333")
  - `type`: 站点类型 (数值, 如1=消防救援站, 2=专职站, 3=战勤保障站)
  - `content`: 详细信息 (文本, 多为空)
  - `code`: 编码 (多为空)

#### `jyz_geojson` - 救援站辖区边界 (141行)
- **业务含义**: 救援站的责任辖区几何边界
- **关键字段**:
  - `geom`: 辖区边界坐标串 (JSON数组格式, 如经纬度点列表)
  - `name`: 救援站名称 (如"庄河市青堆消防救援站")
  - `color`: 地图显示颜色 (如"#91d2f2", "#add094")

#### `jyz_geojson_copy1` - 救援站辖区边界副本 (68行)
- **业务含义**: 辖区边界的备份表
- **关键字段**:
  - `geom`: WKT格式的POLYGON几何 (如"POLYGON((121.xxx 38.xxx,...))")
  - `name`: 救援站名称

### 2. 专职消防队相关

#### `fire_fulltime` - 专职消防队信息 (39行)
- **业务含义**: 政府专职消防队的详细信息
- **关键字段**:
  - `id`: 主键 (字符串)
  - `dadui`: 所属大队
  - `name`: 街道/区域名称 (如"双岛湾街道", "新机场政府专职队")
  - `address`: 详细地址
  - `horizontal/vertical`: 经纬度 (数值类型)
  - `statue`: 建设状态 ("已完成", null)
  - `执勤状态`: 执勤状态 ("已执勤", null)
  - `执勤人员/执勤车辆`: 人员车辆数量
  - `建设经费/建筑面积/占地面积`: 建设信息
  - `img_url`: 图片URL

### 3. 消防工作站相关

#### `fire_workstation` - 消防工作站 (149行)
- **业务含义**: 街道级消防工作站信息
- **关键字段**:
  - `region`: 所属区域 (如"西岗区", "中山区")
  - `jiedao`: 所属街道 (如"香炉礁街道", "日新街道")
  - `name`: 工作站名称
  - `address`: 详细地址
  - `horizontal/vertical`: 经纬度
  - `级别`: 工作站级别 ("二级站")
  - `规定人数/工作人员人数`: 人员配置
  - `公务员人数/事业编人数/消防文员人数`: 人员结构

#### `fire_workstation_quantity` - 工作站工作量统计 (745行)
- **业务含义**: 工作站的检查、培训、演练等工作量统计
- **关键字段**:
  - `检查单位数量/发现隐患数量/当场整改数量`: 检查工作统计
  - `组织演练次数/宣传培训次数`: 培训演练统计
  - `统计开始日期/统计结束日期`: 统计周期

### 4. 辖区与行政区划

#### `dadui_geojson` - 大队辖区边界 (16行)
- **业务含义**: 消防大队的管辖区域边界
- **关键字段**:
  - `dadui`: 大队名称 (如"西岗大队", "旅顺大队")
  - `geojson`: 完整的GeoJSON格式边界数据
  - `color`: 显示颜色
  - `center`: 中心点坐标 ("121.612341,38.917218")

#### `jiedao_coordinate` - 街道坐标 (表结构未完全展示)
#### `jiedao_geojson` - 街道边界 (表结构未完全展示)

### 5. 重点单位与设施

#### `key_unit_coordinate` - 重点单位坐标 (4457行)
- **业务含义**: 消防重点监管单位的位置信息
- **关键字段**:
  - `dadui`: 监管大队
  - `name`: 单位名称 (如"大连报业物业管理有限公司")
  - `address`: 详细地址
  - `code`: 单位编码 (如"21002201000402")
  - `horizontal/vertical`: 经纬度 (字符串格式)

#### `tieta_coordinate` - 铁塔坐标 (表中无数据)
- **业务含义**: 通信铁塔等设施位置

#### `iot_coordinate` - IoT设备坐标 (表结构未完全展示)
#### `fireService_coordinate` - 消防服务设施坐标 (表结构未完全展示)

### 6. 协作单位

#### `psb_coordinate` - 派出所坐标 (168行)
- **业务含义**: 公安派出所位置与联系信息
- **关键字段**:
  - `dadui`: 对应消防大队
  - `name`: 派出所名称 (如"光荣派出所")
  - `horizontal/vertical`: 经纬度
  - `派出所民警`: 联系人
  - `电话`: 联系电话
  - `位置是否变更/删除标记`: 数据状态

#### `serverUnit_coordinate` - 服务单位坐标 (0行估计)
- **业务含义**: 政府服务单位位置
- **样本**: 区政府等

## 数据质量与特点

### 坐标系统
- **格式**: 经纬度分别存储为 `horizontal`/`vertical` 字段
- **类型**: 部分为字符串 (`varchar`)，部分为数值 (`float8`)
- **坐标系**: 从样本数据看为 **GCJ-02** (火星坐标系，大连地区经度121.x，纬度38.x-39.x)
- **PostGIS**: 数据库未安装PostGIS，无原生几何类型支持

### 几何数据存储
- **辖区边界**: 存储在 `geojson` 字段中，格式为JSON数组或WKT字符串
- **点位数据**: 经纬度数值对
- **空间索引**: 当前无PostGIS空间索引，需要基于经纬度数值字段建立B-tree索引

### 数据完整性
- **救援站**: 76个救援站，141个辖区边界 (部分站点可能有多个辖区)
- **专职队**: 39个专职消防队
- **工作站**: 149个消防工作站
- **重点单位**: 4457个重点单位
- **派出所**: 168个派出所

## 业务关系映射

### 层级关系
```
消防总队
├── 消防大队 (dadui_geojson: 16个)
    ├── 消防救援站 (jyz_coordinate: 76个)
    ├── 专职消防队 (fire_fulltime: 39个)
    ├── 消防工作站 (fire_workstation: 149个)
    └── 重点单位监管 (key_unit_coordinate: 4457个)
```

### 空间关系
- **辖区覆盖**: 大队辖区 → 救援站辖区 → 街道工作站
- **协作关系**: 救援站 ↔ 派出所 (同区域协作)
- **服务对象**: 救援站/工作站 → 重点单位 (监管服务)

## 技术建议

### 1. 坐标系统一
- 确认所有坐标为GCJ-02系统
- 统一数据类型：建议将字符串坐标转为数值类型
- 建立经纬度复合索引提升空间查询性能

### 2. 几何数据处理
- 解析 `geojson` 字段中的边界数据
- 考虑安装PostGIS或使用应用层几何计算库
- 实现点在多边形内判断 (辖区归属)

### 3. 数据补充建议
- **消火栓数据**: 当前缺失，需要补充
- **道路网络**: 路径规划需要
- **实时状态**: 车辆/人员实时状态
- **历史事件**: 出警记录与响应时间

## 下一步行动
1. 基于此数据字典设计 LangGraph Agent 的状态Schema
2. 定义空间查询工具接口 (最近救援站、辖区判断、覆盖分析)
3. 设计前端地图图层数据格式 (GeoJSON转换)
4. 制定数据治理方案 (坐标统一、索引优化、缺失数据补充)
