{"dependencies": [".", "langchain-openai", "langgraph-supervisor"], "graphs": {"fire_dispatch_supervisor": "./fire_dispatch_agent/supervisor.py:supervisor_graph", "event_parser": "./fire_dispatch_agent/agents/event_parser.py:event_parser_agent", "spatial_analyzer": "./fire_dispatch_agent/agents/spatial_analyzer.py:spatial_analyzer_agent", "dispatch_planner": "./fire_dispatch_agent/agents/dispatch_planner.py:dispatch_planner_agent", "route_evaluator": "./fire_dispatch_agent/agents/route_evaluator.py:route_evaluator_agent", "notification_agent": "./fire_dispatch_agent/agents/notification_agent.py:notification_agent"}, "env": "./.env", "config_schemas": {"fire_dispatch_config": {"type": "object", "properties": {"max_search_radius": {"type": "number", "default": 10.0, "description": "Maximum search radius for nearby resources (km)"}, "max_dispatch_plans": {"type": "integer", "default": 3, "description": "Maximum number of dispatch plans to generate"}, "eta_cache_ttl": {"type": "integer", "default": 300, "description": "ETA cache time-to-live (seconds)"}, "enable_backup_plans": {"type": "boolean", "default": true, "description": "Whether to generate backup dispatch plans"}, "jurisdiction_priority": {"type": "boolean", "default": true, "description": "Prioritize resources from the same jurisdiction"}, "traffic_consideration": {"type": "boolean", "default": true, "description": "Consider real-time traffic in route planning"}}}}}