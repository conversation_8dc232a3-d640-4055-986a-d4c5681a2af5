#!/usr/bin/env python3
"""
灭火指挥调度地图 Agent
基于 LangGraph 的消防救援智能调度系统
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import json
import asyncio
from datetime import datetime

from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolExecutor
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_core.tools import tool

# ==================== 状态定义 ====================

class EventLevel(Enum):
    """事件等级"""
    MINOR = "一般"
    MAJOR = "较大" 
    SERIOUS = "重大"
    CRITICAL = "特别重大"

class StationType(Enum):
    """救援站类型"""
    RESCUE_STATION = 1  # 消防救援站
    FULLTIME_STATION = 2  # 专职消防站
    LOGISTICS_STATION = 3  # 战勤保障站

@dataclass
class EventContext:
    """事件上下文"""
    event_id: str
    location: Tuple[float, float]  # (lng, lat)
    address: str
    level: EventLevel
    description: str
    timestamp: datetime
    building_type: Optional[str] = None
    floors: Optional[int] = None
    hazmat: bool = False

@dataclass
class ResourceUnit:
    """资源单元"""
    id: str
    name: str
    type: str  # 'rescue_station', 'fulltime_station', 'hydrant'
    location: Tuple[float, float]
    dadui: str  # 所属大队
    status: str = "available"  # available, busy, maintenance
    capacity: Dict[str, Any] = None  # 人员、车辆等能力
    
@dataclass
class DispatchPlan:
    """调度方案"""
    plan_id: str
    primary_stations: List[ResourceUnit]
    backup_stations: List[ResourceUnit]
    hydrants: List[ResourceUnit]
    routes: List[Dict[str, Any]]
    eta_minutes: int
    confidence: float
    reasoning: str

@dataclass
class AgentState:
    """Agent 状态"""
    # 输入
    event: Optional[EventContext] = None
    
    # 中间状态
    jurisdiction_dadui: Optional[str] = None
    nearby_stations: List[ResourceUnit] = None
    nearby_hydrants: List[ResourceUnit] = None
    available_resources: List[ResourceUnit] = None
    
    # 输出
    dispatch_plans: List[DispatchPlan] = None
    selected_plan: Optional[DispatchPlan] = None
    
    # 消息历史
    messages: List[BaseMessage] = None
    
    # 错误处理
    errors: List[str] = None

# ==================== 数据库工具 ====================

@tool
async def query_nearby_stations(longitude: float, latitude: float, radius_km: float = 10) -> List[Dict]:
    """查询附近的救援站"""
    from .database import get_db_connection

    query = """
    SELECT
        rank,
        dadui,
        jyz as name,
        CAST(horizontal AS FLOAT) as lng,
        CAST(vertical AS FLOAT) as lat,
        type,
        content
    FROM report_map.jyz_coordinate
    WHERE horizontal IS NOT NULL AND vertical IS NOT NULL
    ORDER BY (
        (CAST(horizontal AS FLOAT) - %s) * (CAST(horizontal AS FLOAT) - %s) +
        (CAST(vertical AS FLOAT) - %s) * (CAST(vertical AS FLOAT) - %s)
    ) ASC
    LIMIT 10;
    """

    try:
        async with get_db_connection() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(query, (longitude, longitude, latitude, latitude))
                rows = await cursor.fetchall()

                return [
                    {
                        "rank": row[0],
                        "dadui": row[1],
                        "name": row[2],
                        "lng": row[3],
                        "lat": row[4],
                        "type": row[5],
                        "content": row[6] or ""
                    } for row in rows
                ]
    except Exception as e:
        print(f"查询救援站失败: {e}")
        # 返回模拟数据作为降级
        return [
            {
                "rank": 18,
                "dadui": "西岗大队",
                "name": "北京街消防救援站",
                "lng": 121.612341,
                "lat": 38.917218,
                "type": 1,
                "content": "地址：大连市西岗区北京街57号,值班电话：0411-83632330"
            }
        ]

@tool 
async def query_nearby_hydrants(longitude: float, latitude: float, radius_km: float = 5) -> List[Dict]:
    """查询附近的消火栓"""
    # 使用你提供的SQL查询
    query = """
    SELECT
        json_build_object (
            'lnglat',
            json_build_array ( combat_training_data.rep_watersource.lon_correct, combat_training_data.rep_watersource.lat_correct ),
            'name',
            combat_training_data.rep_watersource.NAME,
            'style',
            CASE 
                WHEN rep_watersource."type" = '消火栓' THEN 3
                ELSE 4
            END	 
        ) as hydrant_info
    FROM combat_training_data.rep_watersource 
    WHERE rep_watersource."type" IN ('消火栓','消防水鹤')
    AND lon_correct IS NOT NULL AND lat_correct IS NOT NULL
    ORDER BY (
        (lon_correct - %s) * (lon_correct - %s) + 
        (lat_correct - %s) * (lat_correct - %s)
    ) ASC
    LIMIT 20;
    """
    
    # 模拟返回数据
    return [
        {
            "lnglat": [121.615, 38.920],
            "name": "北京街消火栓001",
            "style": 3
        }
    ]

@tool
async def determine_jurisdiction(longitude: float, latitude: float) -> str:
    """判断事件所属辖区大队"""
    # 基于 dadui_geojson 表进行点在多边形内判断
    # 这里需要解析 geojson 字段并进行几何计算
    query = """
    SELECT dadui, geojson 
    FROM report_map.dadui_geojson
    ORDER BY rank;
    """
    
    # 简化实现：基于最近救援站判断
    stations = await query_nearby_stations(longitude, latitude, 50)
    if stations:
        return stations[0]["dadui"]
    return "未知大队"

@tool
async def calculate_route_eta(start_lng: float, start_lat: float, end_lng: float, end_lat: float) -> Dict:
    """计算路径和预计到达时间"""
    # 这里应该调用高德地图路径规划API
    # 模拟返回
    return {
        "distance_km": 3.2,
        "duration_minutes": 8,
        "route_points": [[start_lng, start_lat], [end_lng, end_lat]],
        "traffic_status": "畅通"
    }

# ==================== Agent 节点 ====================

async def parse_event_node(state: AgentState) -> AgentState:
    """事件解析节点"""
    if not state.event:
        state.errors = state.errors or []
        state.errors.append("缺少事件信息")
        return state
    
    # 地理编码（如果只有地址没有坐标）
    if not state.event.location and state.event.address:
        # 这里应该调用高德地图地理编码API
        # 模拟设置坐标
        state.event.location = (121.612341, 38.917218)
    
    state.messages = state.messages or []
    state.messages.append(AIMessage(content=f"事件解析完成：{state.event.address}"))
    
    return state

async def spatial_query_node(state: AgentState) -> AgentState:
    """空间检索节点"""
    if not state.event or not state.event.location:
        state.errors = state.errors or []
        state.errors.append("缺少事件位置信息")
        return state
    
    lng, lat = state.event.location
    
    # 判断辖区
    state.jurisdiction_dadui = await determine_jurisdiction(lng, lat)
    
    # 查询附近救援站
    stations_data = await query_nearby_stations(lng, lat)
    state.nearby_stations = [
        ResourceUnit(
            id=str(s["rank"]),
            name=s["name"],
            type="rescue_station",
            location=(s["lng"], s["lat"]),
            dadui=s["dadui"],
            capacity={"content": s.get("content", "")}
        ) for s in stations_data
    ]
    
    # 查询附近消火栓
    hydrants_data = await query_nearby_hydrants(lng, lat)
    state.nearby_hydrants = [
        ResourceUnit(
            id=f"hydrant_{i}",
            name=h["name"],
            type="hydrant", 
            location=tuple(h["lnglat"]),
            dadui="",
            capacity={"style": h["style"]}
        ) for i, h in enumerate(hydrants_data)
    ]
    
    state.messages.append(AIMessage(
        content=f"空间检索完成：辖区{state.jurisdiction_dadui}，"
                f"附近{len(state.nearby_stations)}个救援站，"
                f"{len(state.nearby_hydrants)}个消火栓"
    ))
    
    return state

async def dispatch_planning_node(state: AgentState) -> AgentState:
    """调度规划节点"""
    if not state.nearby_stations:
        state.errors = state.errors or []
        state.errors.append("无可用救援站")
        return state
    
    # 筛选可用资源（辖区优先）
    jurisdiction_stations = [s for s in state.nearby_stations if s.dadui == state.jurisdiction_dadui]
    other_stations = [s for s in state.nearby_stations if s.dadui != state.jurisdiction_dadui]
    
    # 生成主备方案
    primary_stations = jurisdiction_stations[:2] if jurisdiction_stations else other_stations[:2]
    backup_stations = (jurisdiction_stations[2:4] if len(jurisdiction_stations) > 2 
                      else other_stations[2:4] if len(other_stations) > 2 else [])
    
    # 选择最近消火栓
    selected_hydrants = state.nearby_hydrants[:3]
    
    # 计算路径和ETA
    if primary_stations:
        route_info = await calculate_route_eta(
            primary_stations[0].location[0], primary_stations[0].location[1],
            state.event.location[0], state.event.location[1]
        )
        eta = route_info["duration_minutes"]
    else:
        eta = 999
    
    # 生成调度方案
    plan = DispatchPlan(
        plan_id=f"plan_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        primary_stations=primary_stations,
        backup_stations=backup_stations,
        hydrants=selected_hydrants,
        routes=[route_info] if primary_stations else [],
        eta_minutes=eta,
        confidence=0.85,
        reasoning=f"辖区优先原则，选择{state.jurisdiction_dadui}最近可用站点"
    )
    
    state.dispatch_plans = [plan]
    state.selected_plan = plan
    
    state.messages.append(AIMessage(
        content=f"调度方案生成完成：主力{len(primary_stations)}站，"
                f"备用{len(backup_stations)}站，ETA {eta}分钟"
    ))
    
    return state

async def output_node(state: AgentState) -> AgentState:
    """输出节点"""
    if state.selected_plan:
        output = {
            "event_id": state.event.event_id,
            "dispatch_plan": asdict(state.selected_plan),
            "timestamp": datetime.now().isoformat()
        }
        
        state.messages.append(AIMessage(
            content=f"调度指令已生成：{json.dumps(output, ensure_ascii=False, indent=2)}"
        ))
    
    return state

# ==================== Agent 构建 ====================

def create_fire_dispatch_agent():
    """创建消防调度Agent"""
    
    # 定义工具
    tools = [query_nearby_stations, query_nearby_hydrants, determine_jurisdiction, calculate_route_eta]
    tool_executor = ToolExecutor(tools)
    
    # 创建状态图
    workflow = StateGraph(AgentState)
    
    # 添加节点
    workflow.add_node("parse_event", parse_event_node)
    workflow.add_node("spatial_query", spatial_query_node) 
    workflow.add_node("dispatch_planning", dispatch_planning_node)
    workflow.add_node("output", output_node)
    
    # 定义边
    workflow.add_edge("parse_event", "spatial_query")
    workflow.add_edge("spatial_query", "dispatch_planning")
    workflow.add_edge("dispatch_planning", "output")
    workflow.add_edge("output", END)
    
    # 设置入口点
    workflow.set_entry_point("parse_event")
    
    return workflow.compile()

# ==================== 使用示例 ====================

async def main():
    """测试Agent"""
    agent = create_fire_dispatch_agent()
    
    # 模拟事件
    event = EventContext(
        event_id="20250821_001",
        location=(121.612341, 38.917218),
        address="大连市西岗区北京街57号",
        level=EventLevel.MAJOR,
        description="商业建筑火灾",
        timestamp=datetime.now(),
        building_type="商业",
        floors=8
    )
    
    # 初始状态
    initial_state = AgentState(
        event=event,
        messages=[HumanMessage(content="接警：大连市西岗区北京街57号发生火灾")]
    )
    
    # 执行Agent
    result = await agent.ainvoke(initial_state)
    
    # 输出结果
    print("=== 调度结果 ===")
    for msg in result.messages:
        print(f"{msg.__class__.__name__}: {msg.content}")
    
    if result.selected_plan:
        print(f"\n=== 最终方案 ===")
        print(f"方案ID: {result.selected_plan.plan_id}")
        print(f"主力站点: {[s.name for s in result.selected_plan.primary_stations]}")
        print(f"备用站点: {[s.name for s in result.selected_plan.backup_stations]}")
        print(f"消火栓: {[h.name for h in result.selected_plan.hydrants]}")
        print(f"预计到达: {result.selected_plan.eta_minutes}分钟")

if __name__ == "__main__":
    asyncio.run(main())
