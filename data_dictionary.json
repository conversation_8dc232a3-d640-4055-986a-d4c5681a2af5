{"dadui_geojson": {"table_name": "<PERSON><PERSON>_geo<PERSON>son", "columns": [{"name": "<PERSON>ui", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "g<PERSON><PERSON><PERSON>", "data_type": "text", "udt_name": "text", "nullable": true, "default": null, "comment": null}, {"name": "rank", "data_type": "smallint", "udt_name": "int2", "nullable": true, "default": null, "comment": null}, {"name": "color", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "center", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [], "approx_rows": 16}, "fire_fulltime": {"table_name": "fire_fulltime", "columns": [{"name": "id", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "comment": null}, {"name": "<PERSON>ui", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "name", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "address", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "statue", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "horizontal", "data_type": "double precision", "udt_name": "float8", "nullable": true, "default": null, "comment": null}, {"name": "vertical", "data_type": "double precision", "udt_name": "float8", "nullable": true, "default": null, "comment": null}, {"name": "rank", "data_type": "integer", "udt_name": "int4", "nullable": true, "default": null, "comment": null}, {"name": "执勤状态", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "新招录人员", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "整合人员", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "建设经费", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "新购置车辆", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "整合车辆", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "新购置器材", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "整合器材", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "指挥调度权", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "人员招录权", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "经费使用权", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "jyz_name", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "建队时间", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "占地面积", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "建筑面积", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "楼层数", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "执勤车辆", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "执勤人员", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "建设方式", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "原管理单位", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "img_url", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [{"name": "fire_fulltime_pkey", "definition": "CREATE UNIQUE INDEX fire_fulltime_pkey ON report_map.fire_fulltime USING btree (id)"}], "approx_rows": 39}, "fire_fulltime_history": {"table_name": "fire_fulltime_history", "columns": [{"name": "id", "data_type": "integer", "udt_name": "int4", "nullable": false, "default": null, "comment": null}, {"name": "<PERSON>ui", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "name", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "address", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "statue", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "horizontal", "data_type": "double precision", "udt_name": "float8", "nullable": true, "default": null, "comment": null}, {"name": "vertical", "data_type": "double precision", "udt_name": "float8", "nullable": true, "default": null, "comment": null}, {"name": "rank", "data_type": "integer", "udt_name": "int4", "nullable": true, "default": null, "comment": null}, {"name": "执勤状态", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "新招录人员", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "整合人员", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "建设经费", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "新购置车辆", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "整合车辆", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "新购置器材", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "整合器材", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "指挥调度权", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "人员招录权", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "经费使用权", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "日期", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "comment": null}], "geometry_columns": [], "indexes": [{"name": "fire_fulltime_copy1_pkey", "definition": "CREATE UNIQUE INDEX fire_fulltime_copy1_pkey ON report_map.fire_fulltime_history USING btree (id, \"日期\")"}], "approx_rows": 56}, "fire_workstation": {"table_name": "fire_workstation", "columns": [{"name": "id", "data_type": "integer", "udt_name": "int4", "nullable": false, "default": null, "comment": null}, {"name": "region", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "<PERSON><PERSON><PERSON>", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "name", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "address", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "horizontal", "data_type": "double precision", "udt_name": "float8", "nullable": true, "default": null, "comment": null}, {"name": "vertical", "data_type": "double precision", "udt_name": "float8", "nullable": true, "default": null, "comment": null}, {"name": "rank", "data_type": "integer", "udt_name": "int4", "nullable": true, "default": null, "comment": null}, {"name": "规定人数", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "工作人员人数", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "公务员人数", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "事业编人数", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "专编专用人数", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "大队", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "级别", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "事业编专编专用人数", "data_type": "integer", "udt_name": "int4", "nullable": true, "default": null, "comment": null}, {"name": "行政编专编专用人数", "data_type": "integer", "udt_name": "int4", "nullable": true, "default": null, "comment": null}, {"name": "消防文员人数", "data_type": "integer", "udt_name": "int4", "nullable": true, "default": null, "comment": null}, {"name": "其他人员", "data_type": "integer", "udt_name": "int4", "nullable": true, "default": null, "comment": null}, {"name": "img_url", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [{"name": "fire_workstation_pkey", "definition": "CREATE UNIQUE INDEX fire_workstation_pkey ON report_map.fire_workstation USING btree (id)"}], "approx_rows": 149}, "fire_workstation_quantity": {"table_name": "fire_workstation_quantity", "columns": [{"name": "序号", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "comment": null}, {"name": "大队", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "乡镇街道", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "现有消防工作站名称", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "工作站类别", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "检查单位数量", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "发现隐患数量", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "当场整改数量", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "移交街道或行业部门数量", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "移交大队数量", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "组织演练次数", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "宣传培训次数", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "会同部门协商次数", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "统计开始日期", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "comment": null}, {"name": "统计结束日期", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "comment": null}, {"name": "rank", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [{"name": "Sheet2_pkey", "definition": "CREATE UNIQUE INDEX \"Sheet2_pkey\" ON report_map.fire_workstation_quantity USING btree (\"序号\", \"统计开始日期\", \"统计结束日期\")"}], "approx_rows": 745}, "fire_workstation_quantity_history": {"table_name": "fire_workstation_quantity_history", "columns": [{"name": "序号", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "comment": null}, {"name": "大队", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "乡镇街道", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "现有消防工作站名称", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "工作站类别", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "检查单位数量", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "发现隐患数量", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "当场整改数量", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "移交街道或行业部门数量", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "移交大队数量", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "组织演练次数", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "宣传培训次数", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "会同部门协商次数", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "日期", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "统计开始日期", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "comment": null}, {"name": "统计结束日期", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "comment": null}, {"name": "rank", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [{"name": "fire_workstation_quantity_history_pkey", "definition": "CREATE UNIQUE INDEX fire_workstation_quantity_history_pkey ON report_map.fire_workstation_quantity_history USING btree (\"序号\", \"统计开始日期\", \"统计结束日期\")"}], "approx_rows": 149}, "fireService_coordinate": {"table_name": "fireService_coordinate", "columns": [{"name": "rank", "data_type": "smallint", "udt_name": "int2", "nullable": true, "default": null, "comment": null}, {"name": "name", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "address", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "horizontal", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "vertical", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [], "approx_rows": 105}, "iot_coordinate": {"table_name": "iot_coordinate", "columns": [{"name": "code", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "name", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "name1", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "vertical", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "horizontal", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [], "approx_rows": 140}, "jdy_pcs_jd": {"table_name": "jdy_pcs_jd", "columns": [{"name": "user_id", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "comment": null}, {"name": "<PERSON>ui", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "jdy", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "p<PERSON><PERSON><PERSON>", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "<PERSON><PERSON><PERSON>", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "remake", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "area", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "更新时间", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "填报人", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [{"name": "jdy_pcs_jd_pkey", "definition": "CREATE UNIQUE INDEX jdy_pcs_jd_pkey ON report_map.jdy_pcs_jd USING btree (user_id)"}], "approx_rows": 118}, "jiedao_coordinate": {"table_name": "jiedao_coordinate", "columns": [{"name": "rank", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "区域", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "name", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "horizontal", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "vertical", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "街道名称", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "街道联系人", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "电话", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "大队", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "位置是否变更", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "备注", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "删除标记", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "创建时间", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "更新时间", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "填报人", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "ID", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "comment": null}, {"name": "辖区范围", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [{"name": "jiedao_coordinate_pkey", "definition": "CREATE UNIQUE INDEX jiedao_coordinate_pkey ON report_map.jiedao_coordinate USING btree (\"ID\")"}], "approx_rows": 152}, "jiedao_geojson": {"table_name": "jieda<PERSON>_geo<PERSON>son", "columns": [{"name": "<PERSON><PERSON><PERSON>", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "g<PERSON><PERSON><PERSON>", "data_type": "text", "udt_name": "text", "nullable": true, "default": null, "comment": null}, {"name": "rank", "data_type": "smallint", "udt_name": "int2", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [], "approx_rows": 0}, "jyz_coordinate": {"table_name": "jyz_coordinate", "columns": [{"name": "rank", "data_type": "smallint", "udt_name": "int2", "nullable": true, "default": null, "comment": null}, {"name": "<PERSON>ui", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "jyz", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "horizontal", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "vertical", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "type", "data_type": "smallint", "udt_name": "int2", "nullable": true, "default": null, "comment": null}, {"name": "content", "data_type": "text", "udt_name": "text", "nullable": true, "default": null, "comment": null}, {"name": "code", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [], "approx_rows": 76}, "jyz_geojson": {"table_name": "jyz_geo<PERSON>son", "columns": [{"name": "geom", "data_type": "text", "udt_name": "text", "nullable": true, "default": null, "comment": null}, {"name": "name", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "color", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [], "approx_rows": 141}, "jyz_geojson_copy1": {"table_name": "jyz_geo<PERSON><PERSON>_copy1", "columns": [{"name": "geom", "data_type": "text", "udt_name": "text", "nullable": true, "default": null, "comment": null}, {"name": "name", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [], "approx_rows": 68}, "key_unit_coordinate": {"table_name": "key_unit_coordinate", "columns": [{"name": "<PERSON>ui", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "name", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "address", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "code", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "horizontal", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "vertical", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [], "approx_rows": 4457}, "psb_coordinate": {"table_name": "psb_coordinate", "columns": [{"name": "rank", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "<PERSON>ui", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "name", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "horizontal", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "vertical", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "派出所民警", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "电话", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "位置是否变更", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "备注", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "删除标记", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "创建时间", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "更新时间", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "填报人", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "ID", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "comment": null}], "geometry_columns": [], "indexes": [{"name": "psb_coordinate_pkey", "definition": "CREATE UNIQUE INDEX psb_coordinate_pkey ON report_map.psb_coordinate USING btree (\"ID\")"}], "approx_rows": 168}, "serverUnit_coordinate": {"table_name": "serverUnit_coordinate", "columns": [{"name": "name", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "tel", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "horizontal", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "vertical", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [], "approx_rows": 0}, "test": {"table_name": "test", "columns": [{"name": "name", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "rank", "data_type": "smallint", "udt_name": "int2", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [], "approx_rows": 0}, "tieta_coordinate": {"table_name": "tieta_coordinate", "columns": [{"name": "区域", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "name", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "horizontal", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "vertical", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [], "approx_rows": 0}, "街道": {"table_name": "街道", "columns": [{"name": "大队", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "监督人员", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "对接乡镇（街道）", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "id", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [], "approx_rows": 170}, "派出所": {"table_name": "派出所", "columns": [{"name": "监督人员", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "派出所", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "大队", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}, {"name": "id", "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "comment": null}], "geometry_columns": [], "indexes": [], "approx_rows": 182}}