# 灭火指挥调度地图 Agent — 任务计划

## 1. 背景与目标
- 底座：高德地图（Amap）
- 后端：LangGraph 调度 Agent
- 数据：Postgres + PostGIS（已存消火栓、救援站、辖区范围等）
- 目标：实现接警→态势研判→资源调度→路径规划→执行追踪→复盘的闭环，MVP 2–4 周可用。

## 2. 范围与不做事项
- 范围：地图可视化（站/栓/辖区）、接警到方案（主/备）、路径与ETA评估、供水链路（初版）、复盘留痕。
- 不做：复杂优化器/仿真、全量移动端、跨省级大规模协同（后续阶段）。

## 3. 架构概览
- 前端：Amap JS/GL、GeoJSON图层、方案对比面板、SSE/WebSocket。
- 后端：API + LangGraph（事件解析→空间检索→调度规划→路径评估→推送→复盘）。
- 数据：Postgres+PostGIS（主）、Redis（缓存）、对象存储（日志/快照）。
- 外部：高德路线/路况/地理编码 API。

## 4. LangGraph 节点与工具
- 节点：事件解析、空间检索、调度规划、路径评估、供水链路、决策锁定、推送交互、复盘。
- 工具：db.query_spatial、db.lock_resource、amap.route、amap.traffic、amap.geocode、cache.get/set、notify.push。
- 状态：事件上下文、资源快照、评估指标、决策结果、执行回执。

## 5. 里程碑
- M1（第1–2周）MVP
  - Amap 底图叠加站/栓/辖区、基本查询与聚合
  - 接警→主/备方案→路径展示→推送
  - 数据治理：坐标系统一、空间索引、核心字段补齐
- M2（第3–4周）增强
  - 等时圈缓存、ETA校正、供水链路初版
  - 资源占用/释放、复盘面板
- M3（第5–8周）进阶
  - 跨区协同策略、复杂中继供水、移动端适配

## 6. 验收标准（MVP）
- 2秒内输出可执行主/备方案（P50），5秒内P95
- 路径可视，含ETA与资源清单；可一键推送
- 数据一致：坐标无明显偏移，空间查询正确
- 全链路留痕可复盘

## 7. 风险与缓解
- 高德限流/不可用 → 本地缓存、退化路线
- 数据质量（栓位可用性/车辆状态缺失）→ 字段补齐、状态治理流程
- 坐标系混用 → 统一为 GCJ-02 或服务层转换

## 8. 任务清单
- A. 数据治理
  - [ ] 盘点 report_map 模式表结构与关键字段
  - [ ] 坐标系核验与统一（GCJ-02/转换）
  - [ ] PostGIS 空间索引与热点缓存策略
  - [ ] 栓位/站点可用性字段补齐（状态、口径、检修时间等）
- B. 后端（LangGraph + API）
  - [ ] 定义 Agent 状态Schema与节点流程
  - [ ] 封装 db/amap/cache/notify 工具接口
  - [ ] 事件→空间检索→调度→路径→推送主干链路
  - [ ] 资源占用与并发控制
- C. 前端（Amap）
  - [ ] 图层渲染（站/栓/辖区）、点聚合、告警闪烁
  - [ ] 方案对比面板（主/备、ETA、资源）
  - [ ] 实时推送与状态刷新
- D. 算法/规则
  - [ ] 辖区优先、最近可用站、就近栓位规则
  - [ ] 等时圈与ETA校正（缓存）
  - [ ] 供水链路初版（源-中继-火点）
- E. 复盘与可观测性
  - [ ] 指标采集（预测vs实际、覆盖率、被覆盖率）
  - [ ] 方案与执行留痕、审计日志

## 9. 交付物
- 前端：MVP 可交互界面与方案对比面板
- 后端：LangGraph 流程与工具库、API Swagger
- 数据：数据字典、空间索引与缓存策略说明
- 运维：配置与密钥管理、限流与退化策略

## 10. 下一步
- 任务1：检查 report_map 模式下的数据结构（表/列/几何字段/索引）
- 任务2：输出 Agent 状态Schema与工具接口契约草案（基于实际数据）
- 任务3：搭建 MVP 主干链路（事件→方案→路径→推送）

