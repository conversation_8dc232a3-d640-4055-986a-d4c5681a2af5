-- report_map 模式数据结构自检脚本
-- 作用：列出表、字段、几何列、索引、行数估计与 PostGIS 版本
-- 使用：psql -h <host> -p <port> -U <user> -d <db> -f scripts/report_map_introspection.sql -P pager=off
\timing on

-- 0) 版本信息
SELECT version() AS postgres_version;
SELECT PostGIS_Full_Version() AS postgis_version;

-- 1) 列出 report_map 模式下的“基础表”
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'report_map' AND table_type = 'BASE TABLE'
ORDER BY 1;

-- 2) 列出所有列（含数据类型）
SELECT table_name,
       ordinal_position,
       column_name,
       data_type,
       udt_name,
       is_nullable,
       col_description(format('%s.%s', table_schema, table_name)::regclass::oid, ordinal_position) AS column_comment
FROM information_schema.columns
WHERE table_schema = 'report_map'
ORDER BY table_name, ordinal_position;

-- 3) 几何列（来自 geometry_columns 视图，如可用）
SELECT f_table_name      AS table_name,
       f_geometry_column AS geom_column,
       type              AS geom_type,
       coord_dimension,
       srid
FROM public.geometry_columns
WHERE f_table_schema = 'report_map'
ORDER BY 1,2;

-- 4) 通过 information_schema 识别 geometry/geography 列（兼容性补充）
SELECT c.table_name,
       c.column_name,
       c.udt_name AS udt,
       CASE
         WHEN c.udt_name IN ('geometry','geography') THEN c.udt_name
         ELSE NULL
       END AS geo_type
FROM information_schema.columns c
WHERE c.table_schema = 'report_map'
  AND c.udt_name IN ('geometry','geography')
ORDER BY 1,2;

-- 5) 索引（包含 GiST/GINX 空间索引）
SELECT t.relname AS table_name,
       i.relname AS index_name,
       pg_get_indexdef(ix.indexrelid) AS index_def
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_namespace n ON n.oid = t.relnamespace
WHERE n.nspname = 'report_map'
ORDER BY 1, 2;

-- 6) 近似行数（基于统计信息）
SELECT relname AS table_name,
       reltuples::bigint AS approx_rows
FROM pg_class c
JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE n.nspname = 'report_map'
  AND relkind = 'r'
ORDER BY approx_rows DESC, table_name;

-- 7) 每张表的主键/唯一约束
SELECT tc.table_name,
       tc.constraint_name,
       tc.constraint_type,
       kcu.column_name
FROM information_schema.table_constraints tc
LEFT JOIN information_schema.key_column_usage kcu
  ON tc.constraint_name = kcu.constraint_name
 AND tc.table_schema = kcu.table_schema
WHERE tc.table_schema = 'report_map'
  AND tc.constraint_type IN ('PRIMARY KEY','UNIQUE')
ORDER BY 1,2,4;

-- 8) 外键（潜在的关联关系）
SELECT tc.table_name AS table_name,
       kcu.column_name AS column_name,
       ccu.table_name AS foreign_table_name,
       ccu.column_name AS foreign_column_name,
       tc.constraint_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
  ON tc.constraint_name = kcu.constraint_name AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
  ON ccu.constraint_name = tc.constraint_name AND ccu.table_schema = tc.table_schema
WHERE tc.table_schema = 'report_map'
  AND tc.constraint_type = 'FOREIGN KEY'
ORDER BY 1,2;

-- 9) 几何列 SRID 与类型抽样（按统计信息选取每表首列，避免全表扫描）
-- 注意：若表为空则可能返回空结果。
WITH geom_cols AS (
  SELECT c.table_name, c.column_name
  FROM information_schema.columns c
  WHERE c.table_schema = 'report_map' AND c.udt_name = 'geometry'
), any_row AS (
  SELECT g.table_name,
         g.column_name,
         (SELECT (p).geom
          FROM (
            SELECT (row_number() OVER ()) AS rn, t.*
            FROM (
              SELECT (x).* FROM (
                SELECT ROW(t.*) AS x FROM (
                  SELECT * FROM (
                    SELECT * FROM report_map."dummy"
                  ) AS z
                ) AS t
              ) q
            ) w
          ) s
         ) AS sample_geom
  FROM geom_cols g
  LIMIT 0
)
-- 上述复杂写法易引发权限/性能问题，这里改为动态 SQL 生成方案，请使用下方脚本单独检查某张表：
-- 示例：将 <table> 与 <geom_col> 替换为实际名称
-- SELECT '<table>' AS table_name,
--        '<geom_col>' AS geom_column,
--        GeometryType((SELECT <geom_col> FROM report_map."<table>" WHERE <geom_col> IS NOT NULL LIMIT 1)) AS sample_geom_type,
--        ST_SRID((SELECT <geom_col> FROM report_map."<table>" WHERE <geom_col> IS NOT NULL LIMIT 1)) AS sample_srid;

-- 10) 可选：统计几何类型分布（按某张具体表）
-- 示例：
-- SELECT GeometryType(geom) AS gtype, COUNT(*)
-- FROM report_map."<your_geometry_table>"
-- GROUP BY 1
-- ORDER BY 2 DESC;

