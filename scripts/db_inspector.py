#!/usr/bin/env python3
"""
数据库结构检查脚本
用于分析 report_map 模式下的表结构、几何字段、索引等信息
"""

import psycopg2
import json
from typing import Dict, List, Any
import sys

# 数据库连接配置
DB_CONFIG = {
    'host': '*************',
    'port': '18640',
    'user': 'zhaoyingnan',
    'password': 'zhaoyingnan@119',
    'database': 'integrated_service_governance'
}

def connect_db():
    """连接数据库"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        sys.exit(1)

def get_tables(cursor) -> List[str]:
    """获取 report_map 模式下的所有表"""
    cursor.execute("""
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'report_map' AND table_type = 'BASE TABLE'
        ORDER BY table_name;
    """)
    return [row[0] for row in cursor.fetchall()]

def get_table_structure(cursor, table_name: str) -> Dict[str, Any]:
    """获取表结构信息"""
    # 获取列信息
    cursor.execute("""
        SELECT column_name, data_type, udt_name, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_schema = 'report_map' AND table_name = %s
        ORDER BY ordinal_position;
    """, (table_name,))

    columns = []
    for row in cursor.fetchall():
        columns.append({
            'name': row[0],
            'data_type': row[1],
            'udt_name': row[2],
            'nullable': row[3] == 'YES',
            'default': row[4],
            'comment': None  # 简化处理
        })

    # 尝试获取几何列信息（如果PostGIS可用）
    geometry_columns = []
    try:
        cursor.execute("""
            SELECT f_geometry_column, type, coord_dimension, srid
            FROM public.geometry_columns
            WHERE f_table_schema = 'report_map' AND f_table_name = %s;
        """, (table_name,))

        for row in cursor.fetchall():
            geometry_columns.append({
                'column': row[0],
                'type': row[1],
                'dimension': row[2],
                'srid': row[3]
            })
    except Exception as e:
        print(f"  注意: 无法获取几何列信息 ({e})")
        # 重置事务状态
        cursor.connection.rollback()
    
    # 获取索引信息
    cursor.execute("""
        SELECT i.relname AS index_name,
               pg_get_indexdef(ix.indexrelid) AS index_def
        FROM pg_class t
        JOIN pg_index ix ON t.oid = ix.indrelid
        JOIN pg_class i ON i.oid = ix.indexrelid
        JOIN pg_namespace n ON n.oid = t.relnamespace
        WHERE n.nspname = 'report_map' AND t.relname = %s
        ORDER BY i.relname;
    """, (table_name,))
    
    indexes = []
    for row in cursor.fetchall():
        indexes.append({
            'name': row[0],
            'definition': row[1]
        })
    
    # 获取行数估计
    cursor.execute("""
        SELECT reltuples::bigint AS approx_rows
        FROM pg_class c
        JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE n.nspname = 'report_map' AND c.relname = %s;
    """, (table_name,))
    
    row_count = cursor.fetchone()
    approx_rows = row_count[0] if row_count else 0
    
    return {
        'table_name': table_name,
        'columns': columns,
        'geometry_columns': geometry_columns,
        'indexes': indexes,
        'approx_rows': approx_rows
    }

def get_sample_data(cursor, table_name: str, limit: int = 3) -> List[Dict]:
    """获取表的样本数据"""
    try:
        cursor.execute(f'SELECT * FROM report_map."{table_name}" LIMIT %s;', (limit,))
        columns = [desc[0] for desc in cursor.description]
        rows = cursor.fetchall()

        samples = []
        for row in rows:
            sample = {}
            for i, col in enumerate(columns):
                value = row[i]
                # 处理特殊类型
                if hasattr(value, '__geo_interface__'):  # 几何类型
                    sample[col] = f"<geometry: {type(value).__name__}>"
                elif isinstance(value, (bytes, memoryview)):
                    sample[col] = f"<binary: {len(value)} bytes>"
                else:
                    sample[col] = value
            samples.append(sample)

        return samples
    except Exception as e:
        cursor.connection.rollback()  # 重置事务状态
        return [{'error': str(e)}]

def main():
    """主函数"""
    print("=== 数据库结构检查开始 ===\n")
    
    conn = connect_db()
    cursor = conn.cursor()
    
    try:
        # 获取版本信息
        cursor.execute("SELECT version();")
        pg_version = cursor.fetchone()[0]
        print(f"PostgreSQL版本: {pg_version}\n")
        
        try:
            cursor.execute("SELECT PostGIS_Full_Version();")
            postgis_version = cursor.fetchone()[0]
            print(f"PostGIS版本: {postgis_version}\n")
        except Exception as e:
            print(f"PostGIS未安装或不可用: {e}\n")
            # 重置连接状态
            conn.rollback()
        
        # 获取所有表
        tables = get_tables(cursor)
        print(f"发现 {len(tables)} 个表: {', '.join(tables)}\n")
        
        # 分析每个表
        all_tables_info = {}
        for table in tables:
            print(f"=== 分析表: {table} ===")
            table_info = get_table_structure(cursor, table)
            
            print(f"行数估计: {table_info['approx_rows']}")
            print(f"列数: {len(table_info['columns'])}")
            
            if table_info['geometry_columns']:
                print("几何列:")
                for geom in table_info['geometry_columns']:
                    print(f"  - {geom['column']}: {geom['type']} (SRID: {geom['srid']})")
            
            print("列信息:")
            for col in table_info['columns']:
                nullable = "NULL" if col['nullable'] else "NOT NULL"
                print(f"  - {col['name']}: {col['data_type']} ({col['udt_name']}) {nullable}")
            
            if table_info['indexes']:
                print("索引:")
                for idx in table_info['indexes']:
                    print(f"  - {idx['name']}")
            
            # 获取样本数据
            print("样本数据:")
            samples = get_sample_data(cursor, table)
            for i, sample in enumerate(samples, 1):
                print(f"  样本 {i}: {json.dumps(sample, ensure_ascii=False, default=str)}")
            
            all_tables_info[table] = table_info
            print()
        
        # 保存完整结果到文件
        with open('data_dictionary.json', 'w', encoding='utf-8') as f:
            json.dump(all_tables_info, f, ensure_ascii=False, indent=2, default=str)
        
        print("=== 检查完成 ===")
        print("详细结果已保存到 data_dictionary.json")
        
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    main()
