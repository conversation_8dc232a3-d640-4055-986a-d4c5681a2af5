[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "fire-dispatch-agent"
version = "0.1.0"
description = "智能消防救援调度系统 - 基于 LangGraph Supervisor 模式的多 Agent 协作平台"
authors = [
    {name = "Fire Dispatch Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.11"
keywords = ["langgraph", "fire-dispatch", "emergency-response", "multi-agent", "supervisor"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

dependencies = [
    # LangGraph 核心
    "langgraph>=0.6.0",
    "langgraph-supervisor>=0.1.0",
    "langchain-core>=0.3.0",
    "langchain-openai>=0.2.0",
    
    # Web 框架
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "websockets>=12.0",
    
    # 数据库
    "psycopg2-binary>=2.9.0",
    "asyncpg>=0.29.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.13.0",
    
    # 缓存
    "redis>=5.0.0",
    "aioredis>=2.0.0",
    
    # 数据处理
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "geopy>=2.4.0",
    "shapely>=2.0.0",
    
    # HTTP 客户端
    "httpx>=0.25.0",
    "aiohttp>=3.9.0",
    
    # 工具库
    "python-multipart>=0.0.6",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-dotenv>=1.0.0",
    "structlog>=23.2.0",
    "rich>=13.7.0",
]

[project.optional-dependencies]
dev = [
    # 测试
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "httpx>=0.25.0",  # for testing FastAPI
    
    # 代码质量
    "black>=23.0.0",
    "ruff>=0.1.0",
    "mypy>=1.7.0",
    "pre-commit>=3.6.0",
    
    # 文档
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
    "mkdocstrings[python]>=0.24.0",
    
    # 开发工具
    "ipython>=8.17.0",
    "jupyter>=1.0.0",
    "watchfiles>=0.21.0",
]

production = [
    # 生产环境监控
    "prometheus-client>=0.19.0",
    "sentry-sdk[fastapi]>=1.38.0",
    
    # 性能优化
    "gunicorn>=21.2.0",
    "uvloop>=0.19.0",
    "orjson>=3.9.0",
]

[project.urls]
Homepage = "https://github.com/fire-dispatch/fire-dispatch-agent"
Documentation = "https://fire-dispatch.github.io/fire-dispatch-agent/"
Repository = "https://github.com/fire-dispatch/fire-dispatch-agent.git"
Issues = "https://github.com/fire-dispatch/fire-dispatch-agent/issues"

[project.scripts]
fire-dispatch = "fire_dispatch_agent.cli:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["fire_dispatch_agent*"]
exclude = ["tests*", "docs*", "scripts*"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/**/*" = ["B011"]

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "psycopg2.*",
    "redis.*",
    "geopy.*",
    "shapely.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["fire_dispatch_agent"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/conftest.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
