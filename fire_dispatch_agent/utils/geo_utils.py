"""
地理计算工具

提供距离计算、点在多边形内判断、边界框计算等几何工具
"""

import math
from typing import List, Tuple, Optional, Dict, Any
import json


def calculate_distance(point1: Tu<PERSON>[float, float], point2: <PERSON><PERSON>[float, float]) -> float:
    """
    计算两点间的球面距离 (Haversine公式)
    
    Args:
        point1: 第一个点 (经度, 纬度)
        point2: 第二个点 (经度, 纬度)
    
    Returns:
        距离 (公里)
    """
    lng1, lat1 = point1
    lng2, lat2 = point2
    
    # 转换为弧度
    lat1_rad = math.radians(lat1)
    lat2_rad = math.radians(lat2)
    delta_lat = math.radians(lat2 - lat1)
    delta_lng = math.radians(lng2 - lng1)
    
    # Haversine公式
    a = (math.sin(delta_lat / 2) ** 2 + 
         math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(delta_lng / 2) ** 2)
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    
    # 地球半径 (公里)
    earth_radius = 6371.0
    
    return earth_radius * c


def point_in_polygon(point: Tuple[float, float], polygon: List[Tuple[float, float]]) -> bool:
    """
    判断点是否在多边形内 (射线法)
    
    Args:
        point: 测试点 (经度, 纬度)
        polygon: 多边形顶点列表 [(经度, 纬度), ...]
    
    Returns:
        是否在多边形内
    """
    if len(polygon) < 3:
        return False
    
    x, y = point
    n = len(polygon)
    inside = False
    
    p1x, p1y = polygon[0]
    for i in range(1, n + 1):
        p2x, p2y = polygon[i % n]
        if y > min(p1y, p2y):
            if y <= max(p1y, p2y):
                if x <= max(p1x, p2x):
                    if p1y != p2y:
                        xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                    if p1x == p2x or x <= xinters:
                        inside = not inside
        p1x, p1y = p2x, p2y
    
    return inside


def get_bounding_box(points: List[Tuple[float, float]], 
                    padding_km: float = 0) -> Dict[str, float]:
    """
    获取点集的边界框
    
    Args:
        points: 点列表 [(经度, 纬度), ...]
        padding_km: 边界扩展距离 (公里)
    
    Returns:
        边界框 {"min_lng": float, "max_lng": float, "min_lat": float, "max_lat": float}
    """
    if not points:
        return {"min_lng": 0, "max_lng": 0, "min_lat": 0, "max_lat": 0}
    
    lngs = [p[0] for p in points]
    lats = [p[1] for p in points]
    
    min_lng, max_lng = min(lngs), max(lngs)
    min_lat, max_lat = min(lats), max(lats)
    
    if padding_km > 0:
        # 经纬度每度对应的公里数 (近似)
        lat_deg_per_km = 1 / 111.0
        lng_deg_per_km = 1 / (111.0 * math.cos(math.radians((min_lat + max_lat) / 2)))
        
        padding_lat = padding_km * lat_deg_per_km
        padding_lng = padding_km * lng_deg_per_km
        
        min_lng -= padding_lng
        max_lng += padding_lng
        min_lat -= padding_lat
        max_lat += padding_lat
    
    return {
        "min_lng": min_lng,
        "max_lng": max_lng,
        "min_lat": min_lat,
        "max_lat": max_lat
    }


def parse_geojson_polygon(geojson_str: str) -> Optional[List[Tuple[float, float]]]:
    """
    解析 GeoJSON 多边形字符串
    
    Args:
        geojson_str: GeoJSON 字符串或坐标数组字符串
    
    Returns:
        多边形顶点列表或 None
    """
    try:
        # 尝试解析为 JSON
        data = json.loads(geojson_str) if isinstance(geojson_str, str) else geojson_str
        
        # 处理不同格式
        if isinstance(data, list):
            # 直接是坐标数组
            if len(data) > 0 and isinstance(data[0], list) and len(data[0]) == 2:
                return [(point[0], point[1]) for point in data]
        
        elif isinstance(data, dict):
            # GeoJSON 格式
            if data.get("type") == "Polygon":
                coordinates = data.get("coordinates", [])
                if coordinates and len(coordinates) > 0:
                    # 取外环 (第一个环)
                    outer_ring = coordinates[0]
                    return [(point[0], point[1]) for point in outer_ring]
            
            elif data.get("type") == "Feature":
                geometry = data.get("geometry", {})
                if geometry.get("type") == "Polygon":
                    coordinates = geometry.get("coordinates", [])
                    if coordinates and len(coordinates) > 0:
                        outer_ring = coordinates[0]
                        return [(point[0], point[1]) for point in outer_ring]
        
        return None
    
    except (json.JSONDecodeError, KeyError, IndexError, TypeError):
        return None


def parse_wkt_polygon(wkt_str: str) -> Optional[List[Tuple[float, float]]]:
    """
    解析 WKT POLYGON 字符串
    
    Args:
        wkt_str: WKT 格式字符串，如 "POLYGON((lng lat, lng lat, ...))"
    
    Returns:
        多边形顶点列表或 None
    """
    try:
        # 移除 POLYGON(( 和 ))
        wkt_str = wkt_str.strip()
        if not wkt_str.upper().startswith("POLYGON"):
            return None
        
        # 提取坐标部分
        start = wkt_str.find("((")
        end = wkt_str.rfind("))")
        if start == -1 or end == -1:
            return None
        
        coords_str = wkt_str[start + 2:end]
        
        # 解析坐标点
        points = []
        for point_str in coords_str.split(","):
            point_str = point_str.strip()
            if point_str:
                parts = point_str.split()
                if len(parts) >= 2:
                    lng = float(parts[0])
                    lat = float(parts[1])
                    points.append((lng, lat))
        
        return points if len(points) >= 3 else None
    
    except (ValueError, IndexError):
        return None


def get_polygon_center(polygon: List[Tuple[float, float]]) -> Tuple[float, float]:
    """
    计算多边形的几何中心 (质心)
    
    Args:
        polygon: 多边形顶点列表
    
    Returns:
        中心点 (经度, 纬度)
    """
    if not polygon:
        return (0.0, 0.0)
    
    # 简单的算术平均 (对于小范围多边形足够准确)
    total_lng = sum(p[0] for p in polygon)
    total_lat = sum(p[1] for p in polygon)
    count = len(polygon)
    
    return (total_lng / count, total_lat / count)


def get_polygon_area(polygon: List[Tuple[float, float]]) -> float:
    """
    计算多边形面积 (平方公里，近似)
    
    Args:
        polygon: 多边形顶点列表
    
    Returns:
        面积 (平方公里)
    """
    if len(polygon) < 3:
        return 0.0
    
    # 使用 Shoelace 公式计算面积 (度数单位)
    area_deg = 0.0
    n = len(polygon)
    
    for i in range(n):
        j = (i + 1) % n
        area_deg += polygon[i][0] * polygon[j][1]
        area_deg -= polygon[j][0] * polygon[i][1]
    
    area_deg = abs(area_deg) / 2.0
    
    # 转换为平方公里 (近似)
    # 1度经度 ≈ 111km * cos(纬度)
    # 1度纬度 ≈ 111km
    center_lat = sum(p[1] for p in polygon) / len(polygon)
    km_per_deg_lng = 111.0 * math.cos(math.radians(center_lat))
    km_per_deg_lat = 111.0
    
    area_km2 = area_deg * km_per_deg_lng * km_per_deg_lat
    
    return area_km2


def create_circle_polygon(center: Tuple[float, float], radius_km: float, 
                         num_points: int = 32) -> List[Tuple[float, float]]:
    """
    创建圆形多边形 (用于等距圈)
    
    Args:
        center: 圆心 (经度, 纬度)
        radius_km: 半径 (公里)
        num_points: 多边形顶点数
    
    Returns:
        圆形多边形顶点列表
    """
    lng, lat = center
    
    # 经纬度每度对应的公里数
    lat_deg_per_km = 1 / 111.0
    lng_deg_per_km = 1 / (111.0 * math.cos(math.radians(lat)))
    
    points = []
    for i in range(num_points):
        angle = 2 * math.pi * i / num_points
        
        # 计算偏移
        delta_lat = radius_km * lat_deg_per_km * math.sin(angle)
        delta_lng = radius_km * lng_deg_per_km * math.cos(angle)
        
        point_lng = lng + delta_lng
        point_lat = lat + delta_lat
        
        points.append((point_lng, point_lat))
    
    # 闭合多边形
    if points:
        points.append(points[0])
    
    return points
