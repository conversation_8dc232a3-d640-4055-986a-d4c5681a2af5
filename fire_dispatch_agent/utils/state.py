"""
LangGraph 状态定义

定义 Agent 间共享的状态结构，支持事件上下文、资源信息、调度方案等
"""

from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from langgraph.graph import MessagesState
from langchain_core.messages import BaseMessage

from ..models.event import EventContext, EventLevel
from ..models.resource import ResourceUnit, StationType
from ..models.dispatch import DispatchPlan, RouteInfo


class AgentStatus(Enum):
    """Agent 执行状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class ProcessingStage(Enum):
    """处理阶段"""
    EVENT_PARSING = "event_parsing"
    SPATIAL_ANALYSIS = "spatial_analysis"
    DISPATCH_PLANNING = "dispatch_planning"
    ROUTE_EVALUATION = "route_evaluation"
    NOTIFICATION = "notification"
    COMPLETED = "completed"


@dataclass
class AgentResult:
    """Agent 执行结果"""
    agent_name: str
    status: AgentStatus
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class SpatialAnalysisResult:
    """空间分析结果"""
    jurisdiction_dadui: Optional[str] = None
    nearby_stations: List[ResourceUnit] = field(default_factory=list)
    nearby_hydrants: List[ResourceUnit] = field(default_factory=list)
    coverage_analysis: Optional[Dict[str, Any]] = None
    search_radius_used: float = 10.0


@dataclass
class DispatchPlanningResult:
    """调度规划结果"""
    primary_plans: List[DispatchPlan] = field(default_factory=list)
    backup_plans: List[DispatchPlan] = field(default_factory=list)
    selected_plan: Optional[DispatchPlan] = None
    planning_reasoning: Optional[str] = None
    confidence_score: float = 0.0


@dataclass
class RouteEvaluationResult:
    """路径评估结果"""
    routes: List[RouteInfo] = field(default_factory=list)
    traffic_conditions: Optional[Dict[str, Any]] = None
    alternative_routes: List[RouteInfo] = field(default_factory=list)
    eta_analysis: Optional[Dict[str, Any]] = None


@dataclass
class NotificationResult:
    """通知结果"""
    notifications_sent: List[Dict[str, Any]] = field(default_factory=list)
    websocket_clients: List[str] = field(default_factory=list)
    external_systems: List[str] = field(default_factory=list)
    notification_status: Dict[str, str] = field(default_factory=dict)


@dataclass
class FireDispatchState(MessagesState):
    """
    消防调度 Agent 状态
    
    继承 MessagesState 以支持消息历史，添加业务特定字段
    """
    
    # ==================== 基础信息 ====================
    session_id: str = ""
    request_id: str = ""
    current_stage: ProcessingStage = ProcessingStage.EVENT_PARSING
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    # ==================== 事件信息 ====================
    event: Optional[EventContext] = None
    event_priority: int = 1  # 1-5, 5为最高优先级
    event_tags: List[str] = field(default_factory=list)
    
    # ==================== 空间分析结果 ====================
    spatial_analysis: Optional[SpatialAnalysisResult] = None
    
    # ==================== 调度规划结果 ====================
    dispatch_planning: Optional[DispatchPlanningResult] = None
    
    # ==================== 路径评估结果 ====================
    route_evaluation: Optional[RouteEvaluationResult] = None
    
    # ==================== 通知结果 ====================
    notification: Optional[NotificationResult] = None
    
    # ==================== Agent 执行状态 ====================
    agent_results: Dict[str, AgentResult] = field(default_factory=dict)
    current_agent: Optional[str] = None
    next_agent: Optional[str] = None
    
    # ==================== 错误处理 ====================
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    # ==================== 配置与上下文 ====================
    config: Dict[str, Any] = field(default_factory=dict)
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    
    # ==================== 缓存数据 ====================
    cached_data: Dict[str, Any] = field(default_factory=dict)
    
    # ==================== 最终输出 ====================
    final_dispatch_plan: Optional[DispatchPlan] = None
    execution_summary: Optional[Dict[str, Any]] = None
    
    def add_agent_result(self, agent_name: str, status: AgentStatus, 
                        result: Optional[Dict[str, Any]] = None, 
                        error: Optional[str] = None,
                        execution_time: Optional[float] = None) -> None:
        """添加 Agent 执行结果"""
        self.agent_results[agent_name] = AgentResult(
            agent_name=agent_name,
            status=status,
            result=result,
            error=error,
            execution_time=execution_time
        )
        self.updated_at = datetime.now()
    
    def get_agent_result(self, agent_name: str) -> Optional[AgentResult]:
        """获取 Agent 执行结果"""
        return self.agent_results.get(agent_name)
    
    def is_agent_completed(self, agent_name: str) -> bool:
        """检查 Agent 是否已完成"""
        result = self.get_agent_result(agent_name)
        return result is not None and result.status == AgentStatus.COMPLETED
    
    def is_agent_failed(self, agent_name: str) -> bool:
        """检查 Agent 是否失败"""
        result = self.get_agent_result(agent_name)
        return result is not None and result.status == AgentStatus.FAILED
    
    def add_error(self, error: str) -> None:
        """添加错误信息"""
        self.errors.append(error)
        self.updated_at = datetime.now()
    
    def add_warning(self, warning: str) -> None:
        """添加警告信息"""
        self.warnings.append(warning)
        self.updated_at = datetime.now()
    
    def set_stage(self, stage: ProcessingStage) -> None:
        """设置当前处理阶段"""
        self.current_stage = stage
        self.updated_at = datetime.now()
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要"""
        completed_agents = [
            name for name, result in self.agent_results.items() 
            if result.status == AgentStatus.COMPLETED
        ]
        failed_agents = [
            name for name, result in self.agent_results.items() 
            if result.status == AgentStatus.FAILED
        ]
        
        total_execution_time = sum(
            result.execution_time or 0 
            for result in self.agent_results.values()
        )
        
        return {
            "session_id": self.session_id,
            "request_id": self.request_id,
            "current_stage": self.current_stage.value,
            "completed_agents": completed_agents,
            "failed_agents": failed_agents,
            "total_agents": len(self.agent_results),
            "success_rate": len(completed_agents) / len(self.agent_results) if self.agent_results else 0,
            "total_execution_time": total_execution_time,
            "error_count": len(self.errors),
            "warning_count": len(self.warnings),
            "has_final_plan": self.final_dispatch_plan is not None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "session_id": self.session_id,
            "request_id": self.request_id,
            "current_stage": self.current_stage.value,
            "event": self.event.__dict__ if self.event else None,
            "spatial_analysis": self.spatial_analysis.__dict__ if self.spatial_analysis else None,
            "dispatch_planning": self.dispatch_planning.__dict__ if self.dispatch_planning else None,
            "route_evaluation": self.route_evaluation.__dict__ if self.route_evaluation else None,
            "notification": self.notification.__dict__ if self.notification else None,
            "agent_results": {
                name: {
                    "agent_name": result.agent_name,
                    "status": result.status.value,
                    "result": result.result,
                    "error": result.error,
                    "execution_time": result.execution_time,
                    "timestamp": result.timestamp.isoformat()
                }
                for name, result in self.agent_results.items()
            },
            "errors": self.errors,
            "warnings": self.warnings,
            "final_dispatch_plan": self.final_dispatch_plan.__dict__ if self.final_dispatch_plan else None,
            "execution_summary": self.get_execution_summary(),
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
