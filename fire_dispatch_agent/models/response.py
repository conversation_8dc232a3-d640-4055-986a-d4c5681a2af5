"""
API 响应模型定义

定义统一的 API 响应格式
"""

from typing import Optional, Any, Dict, List
from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field


class ResponseStatus(Enum):
    """响应状态"""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    PARTIAL = "partial"


class APIResponse(BaseModel):
    """通用 API 响应"""
    
    status: ResponseStatus = Field(..., description="响应状态")
    message: str = Field(..., description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    
    # 元数据
    request_id: Optional[str] = Field(None, description="请求ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")
    processing_time_ms: Optional[int] = Field(None, description="处理时间 (毫秒)")
    
    # 错误信息
    error_code: Optional[str] = Field(None, description="错误代码")
    error_details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    
    # 分页信息 (如适用)
    pagination: Optional[Dict[str, Any]] = Field(None, description="分页信息")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
    
    @classmethod
    def success(cls, data: Any = None, message: str = "操作成功", 
                request_id: Optional[str] = None, 
                processing_time_ms: Optional[int] = None) -> "APIResponse":
        """创建成功响应"""
        return cls(
            status=ResponseStatus.SUCCESS,
            message=message,
            data=data,
            request_id=request_id,
            processing_time_ms=processing_time_ms
        )
    
    @classmethod
    def error(cls, message: str, error_code: Optional[str] = None,
              error_details: Optional[Dict[str, Any]] = None,
              request_id: Optional[str] = None) -> "APIResponse":
        """创建错误响应"""
        return cls(
            status=ResponseStatus.ERROR,
            message=message,
            error_code=error_code,
            error_details=error_details,
            request_id=request_id
        )
    
    @classmethod
    def warning(cls, message: str, data: Any = None,
                request_id: Optional[str] = None) -> "APIResponse":
        """创建警告响应"""
        return cls(
            status=ResponseStatus.WARNING,
            message=message,
            data=data,
            request_id=request_id
        )


class DispatchResponse(BaseModel):
    """调度响应"""
    
    request_id: str = Field(..., description="请求ID")
    event_id: str = Field(..., description="事件ID")
    session_id: str = Field(..., description="会话ID")
    
    # 调度结果
    dispatch_plans: List[Dict[str, Any]] = Field(..., description="调度方案列表")
    recommended_plan: Optional[Dict[str, Any]] = Field(None, description="推荐方案")
    
    # 执行状态
    status: str = Field(..., description="执行状态")
    stage: str = Field(..., description="当前阶段")
    progress: float = Field(..., ge=0, le=1, description="执行进度 (0-1)")
    
    # 分析结果
    spatial_analysis: Optional[Dict[str, Any]] = Field(None, description="空间分析结果")
    resource_analysis: Optional[Dict[str, Any]] = Field(None, description="资源分析结果")
    route_analysis: Optional[Dict[str, Any]] = Field(None, description="路径分析结果")
    
    # 统计信息
    total_stations_found: int = Field(..., description="发现的救援站总数")
    total_hydrants_found: int = Field(..., description="发现的消火栓总数")
    processing_time_ms: int = Field(..., description="处理时间 (毫秒)")
    
    # 质量指标
    confidence_score: float = Field(..., ge=0, le=1, description="置信度")
    coverage_score: float = Field(..., ge=0, le=1, description="覆盖度")
    
    # 消息和警告
    messages: List[str] = Field(default_factory=list, description="处理消息")
    warnings: List[str] = Field(default_factory=list, description="警告信息")
    errors: List[str] = Field(default_factory=list, description="错误信息")
    
    # 时间信息
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ResourceListResponse(BaseModel):
    """资源列表响应"""
    
    resources: List[Dict[str, Any]] = Field(..., description="资源列表")
    total_count: int = Field(..., description="总数量")
    
    # 查询参数
    search_location: tuple[float, float] = Field(..., description="搜索位置")
    search_radius: float = Field(..., description="搜索半径")
    resource_types: List[str] = Field(..., description="资源类型")
    
    # 统计信息
    type_distribution: Dict[str, int] = Field(..., description="类型分布")
    dadui_distribution: Dict[str, int] = Field(..., description="大队分布")
    status_distribution: Dict[str, int] = Field(..., description="状态分布")
    
    # 距离统计
    min_distance_km: float = Field(..., description="最近距离")
    max_distance_km: float = Field(..., description="最远距离")
    avg_distance_km: float = Field(..., description="平均距离")


class WebSocketMessage(BaseModel):
    """WebSocket 消息"""
    
    type: str = Field(..., description="消息类型")
    event_id: Optional[str] = Field(None, description="事件ID")
    session_id: Optional[str] = Field(None, description="会话ID")
    data: Any = Field(..., description="消息数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class HealthCheckResponse(BaseModel):
    """健康检查响应"""
    
    status: str = Field(..., description="服务状态")
    version: str = Field(..., description="版本号")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    
    # 组件状态
    database: str = Field(..., description="数据库状态")
    redis: str = Field(..., description="Redis状态")
    external_apis: Dict[str, str] = Field(..., description="外部API状态")
    
    # 系统信息
    uptime_seconds: int = Field(..., description="运行时间 (秒)")
    memory_usage_mb: float = Field(..., description="内存使用 (MB)")
    cpu_usage_percent: float = Field(..., description="CPU使用率 (%)")
    
    # 业务指标
    active_sessions: int = Field(..., description="活跃会话数")
    total_requests: int = Field(..., description="总请求数")
    error_rate: float = Field(..., description="错误率")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
