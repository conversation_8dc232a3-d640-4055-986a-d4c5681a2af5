"""
调度方案模型定义

定义调度计划、路径信息等数据结构
"""

from typing import Optional, List, Dict, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field

from .resource import ResourceUnit


class DispatchStatus(Enum):
    """调度状态"""
    PENDING = "pending"      # 待执行
    DISPATCHED = "dispatched"  # 已派遣
    EN_ROUTE = "en_route"    # 途中
    ON_SCENE = "on_scene"    # 到场
    COMPLETED = "completed"   # 完成
    CANCELLED = "cancelled"   # 取消


class RouteType(Enum):
    """路径类型"""
    FASTEST = "fastest"      # 最快路径
    SHORTEST = "shortest"    # 最短路径
    AVOID_TRAFFIC = "avoid_traffic"  # 避开拥堵
    EMERGENCY = "emergency"   # 应急路径


@dataclass
class RouteInfo:
    """路径信息"""
    route_id: str
    start_location: Tuple[float, float]  # 起点 (lng, lat)
    end_location: Tuple[float, float]    # 终点 (lng, lat)
    route_type: RouteType
    
    # 路径详情
    distance_km: float  # 距离 (公里)
    duration_minutes: int  # 预计时间 (分钟)
    route_points: List[Tuple[float, float]] = field(default_factory=list)  # 路径点
    
    # 交通信息
    traffic_status: str = "unknown"  # 路况状态
    traffic_delay_minutes: int = 0   # 拥堵延误 (分钟)
    
    # 路径描述
    instructions: List[str] = field(default_factory=list)  # 导航指令
    road_names: List[str] = field(default_factory=list)    # 经过道路
    
    # 风险评估
    risk_level: str = "low"  # 风险等级: low, medium, high
    risk_factors: List[str] = field(default_factory=list)  # 风险因素
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    
    def get_total_time(self) -> int:
        """获取总预计时间 (包含延误)"""
        return self.duration_minutes + self.traffic_delay_minutes
    
    def get_arrival_time(self, departure_time: Optional[datetime] = None) -> datetime:
        """获取预计到达时间"""
        if departure_time is None:
            departure_time = datetime.now()
        
        from datetime import timedelta
        return departure_time + timedelta(minutes=self.get_total_time())
    
    def is_optimal(self, other: 'RouteInfo') -> bool:
        """比较是否比另一条路径更优"""
        # 综合考虑时间和风险
        self_score = self.get_total_time()
        other_score = other.get_total_time()
        
        # 风险调整
        risk_penalty = {"low": 0, "medium": 2, "high": 5}
        self_score += risk_penalty.get(self.risk_level, 0)
        other_score += risk_penalty.get(other.risk_level, 0)
        
        return self_score < other_score


@dataclass
class DispatchPlan:
    """调度方案"""
    plan_id: str
    event_id: str
    
    # 资源配置
    primary_stations: List[ResourceUnit] = field(default_factory=list)
    backup_stations: List[ResourceUnit] = field(default_factory=list)
    hydrants: List[ResourceUnit] = field(default_factory=list)
    
    # 路径信息
    routes: List[RouteInfo] = field(default_factory=list)
    
    # 时间预估
    eta_minutes: int = 0  # 预计到达时间
    setup_time_minutes: int = 5  # 现场准备时间
    
    # 方案评估
    confidence: float = 0.0  # 置信度 (0-1)
    priority_score: int = 0  # 优先级分数
    reasoning: str = ""      # 决策理由
    
    # 资源需求匹配
    resource_adequacy: float = 0.0  # 资源充足度 (0-1)
    coverage_score: float = 0.0     # 覆盖评分 (0-1)
    
    # 状态信息
    status: DispatchStatus = DispatchStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    # 执行信息
    dispatch_time: Optional[datetime] = None
    arrival_time: Optional[datetime] = None
    completion_time: Optional[datetime] = None
    
    # 附加信息
    notes: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    def get_total_resources(self) -> Dict[str, int]:
        """获取总资源统计"""
        all_stations = self.primary_stations + self.backup_stations
        
        total_personnel = 0
        total_vehicles = 0
        
        for station in all_stations:
            if station.capacity:
                total_personnel += station.capacity.personnel_count
                total_vehicles += station.capacity.get_total_vehicles()
        
        return {
            "stations": len(all_stations),
            "primary_stations": len(self.primary_stations),
            "backup_stations": len(self.backup_stations),
            "hydrants": len(self.hydrants),
            "personnel": total_personnel,
            "vehicles": total_vehicles
        }
    
    def get_fastest_route(self) -> Optional[RouteInfo]:
        """获取最快路径"""
        if not self.routes:
            return None
        
        return min(self.routes, key=lambda r: r.get_total_time())
    
    def get_overall_score(self) -> float:
        """获取方案综合评分 (0-100)"""
        score = 0
        
        # 置信度权重 (30%)
        score += self.confidence * 30
        
        # 资源充足度权重 (25%)
        score += self.resource_adequacy * 25
        
        # 覆盖评分权重 (20%)
        score += self.coverage_score * 20
        
        # 响应时间权重 (25%) - 时间越短分数越高
        if self.eta_minutes > 0:
            time_score = max(0, 1 - (self.eta_minutes - 300) / 600)  # 5分钟为基准
            score += time_score * 25
        
        return min(100, max(0, score))
    
    def add_note(self, note: str) -> None:
        """添加备注"""
        self.notes.append(note)
        self.updated_at = datetime.now()
    
    def add_warning(self, warning: str) -> None:
        """添加警告"""
        self.warnings.append(warning)
        self.updated_at = datetime.now()
    
    def update_status(self, status: DispatchStatus) -> None:
        """更新状态"""
        self.status = status
        self.updated_at = datetime.now()
        
        if status == DispatchStatus.DISPATCHED and not self.dispatch_time:
            self.dispatch_time = datetime.now()
        elif status == DispatchStatus.ON_SCENE and not self.arrival_time:
            self.arrival_time = datetime.now()
        elif status == DispatchStatus.COMPLETED and not self.completion_time:
            self.completion_time = datetime.now()


class DispatchRequest(BaseModel):
    """调度请求"""
    
    event_id: str = Field(..., description="事件ID")
    max_plans: int = Field(3, ge=1, le=5, description="最大方案数")
    include_backup: bool = Field(True, description="是否包含备用方案")
    jurisdiction_priority: bool = Field(True, description="是否优先本辖区资源")
    max_response_time: int = Field(30, ge=5, le=60, description="最大响应时间 (分钟)")
    
    # 资源约束
    min_stations: int = Field(1, ge=1, description="最少救援站数")
    max_stations: int = Field(5, ge=1, description="最多救援站数")
    min_hydrants: int = Field(1, ge=0, description="最少消火栓数")
    
    # 偏好设置
    prefer_experienced: bool = Field(True, description="优先经验丰富的站点")
    avoid_maintenance: bool = Field(True, description="避开维护中的资源")


class DispatchResponse(BaseModel):
    """调度响应"""
    
    request_id: str = Field(..., description="请求ID")
    event_id: str = Field(..., description="事件ID")
    plans: List[Dict[str, Any]] = Field(..., description="调度方案列表")
    recommended_plan_id: Optional[str] = Field(None, description="推荐方案ID")
    
    # 统计信息
    total_plans: int = Field(..., description="方案总数")
    processing_time_ms: int = Field(..., description="处理时间 (毫秒)")
    
    # 资源统计
    available_stations: int = Field(..., description="可用救援站数")
    available_hydrants: int = Field(..., description="可用消火栓数")
    
    # 覆盖分析
    coverage_analysis: Dict[str, Any] = Field(..., description="覆盖分析")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
