"""
事件模型定义

定义消防事件的数据结构和相关枚举
"""

from typing import Optional, List, Dict, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field, validator


class EventLevel(Enum):
    """事件等级"""
    MINOR = "一般"
    MAJOR = "较大" 
    SERIOUS = "重大"
    CRITICAL = "特别重大"


class EventType(Enum):
    """事件类型"""
    FIRE = "火灾"
    RESCUE = "救援"
    HAZMAT = "危化品"
    MEDICAL = "医疗急救"
    TRAFFIC = "交通事故"
    NATURAL_DISASTER = "自然灾害"
    OTHER = "其他"


class BuildingType(Enum):
    """建筑类型"""
    RESIDENTIAL = "住宅"
    COMMERCIAL = "商业"
    INDUSTRIAL = "工业"
    OFFICE = "办公"
    HOSPITAL = "医院"
    SCHOOL = "学校"
    WAREHOUSE = "仓储"
    HIGH_RISE = "高层建筑"
    UNDERGROUND = "地下建筑"
    OTHER = "其他"


@dataclass
class EventContext:
    """事件上下文"""
    event_id: str
    location: Tuple[float, float]  # (lng, lat)
    address: str
    level: EventLevel
    event_type: EventType
    description: str
    timestamp: datetime
    
    # 可选字段
    building_type: Optional[BuildingType] = None
    floors: Optional[int] = None
    basement_floors: Optional[int] = None
    building_area: Optional[float] = None  # 建筑面积 (平方米)
    
    # 危险因素
    hazmat: bool = False
    hazmat_types: List[str] = field(default_factory=list)
    explosion_risk: bool = False
    collapse_risk: bool = False
    
    # 人员信息
    trapped_people: Optional[int] = None
    evacuated_people: Optional[int] = None
    casualties: Optional[int] = None
    
    # 环境因素
    weather_conditions: Optional[str] = None
    wind_speed: Optional[float] = None  # 风速 (m/s)
    wind_direction: Optional[str] = None
    temperature: Optional[float] = None  # 温度 (摄氏度)
    
    # 报警信息
    caller_name: Optional[str] = None
    caller_phone: Optional[str] = None
    call_time: Optional[datetime] = None
    
    # 附加信息
    images: List[str] = field(default_factory=list)
    videos: List[str] = field(default_factory=list)
    additional_info: Dict[str, Any] = field(default_factory=dict)
    
    def get_priority_score(self) -> int:
        """计算事件优先级分数 (1-10, 10为最高)"""
        base_score = {
            EventLevel.MINOR: 2,
            EventLevel.MAJOR: 5,
            EventLevel.SERIOUS: 7,
            EventLevel.CRITICAL: 9
        }.get(self.level, 1)
        
        # 危险因素加分
        if self.hazmat:
            base_score += 2
        if self.explosion_risk:
            base_score += 2
        if self.collapse_risk:
            base_score += 1
        
        # 人员因素加分
        if self.trapped_people and self.trapped_people > 0:
            base_score += min(self.trapped_people // 5, 3)
        
        # 建筑类型加分
        if self.building_type in [BuildingType.HOSPITAL, BuildingType.SCHOOL]:
            base_score += 1
        elif self.building_type == BuildingType.HIGH_RISE:
            base_score += 1
        
        return min(base_score, 10)
    
    def is_high_priority(self) -> bool:
        """是否为高优先级事件"""
        return self.get_priority_score() >= 7
    
    def get_required_resources(self) -> Dict[str, int]:
        """获取所需资源估算"""
        resources = {
            "rescue_stations": 1,
            "vehicles": 2,
            "personnel": 10
        }
        
        # 根据事件等级调整
        level_multiplier = {
            EventLevel.MINOR: 1.0,
            EventLevel.MAJOR: 1.5,
            EventLevel.SERIOUS: 2.0,
            EventLevel.CRITICAL: 3.0
        }.get(self.level, 1.0)
        
        # 根据建筑类型调整
        if self.building_type == BuildingType.HIGH_RISE:
            level_multiplier *= 1.5
        elif self.building_type in [BuildingType.HOSPITAL, BuildingType.SCHOOL]:
            level_multiplier *= 1.3
        
        # 危险因素调整
        if self.hazmat:
            level_multiplier *= 1.5
        if self.explosion_risk:
            level_multiplier *= 1.3
        
        return {
            key: max(1, int(value * level_multiplier))
            for key, value in resources.items()
        }


class EventRequest(BaseModel):
    """事件请求模型 (API 输入)"""
    
    location: Optional[Tuple[float, float]] = Field(None, description="事件坐标 (经度, 纬度)")
    address: str = Field(..., description="事件地址")
    level: EventLevel = Field(..., description="事件等级")
    event_type: EventType = Field(EventType.FIRE, description="事件类型")
    description: str = Field(..., description="事件描述")
    
    # 可选字段
    building_type: Optional[BuildingType] = Field(None, description="建筑类型")
    floors: Optional[int] = Field(None, ge=1, le=200, description="楼层数")
    basement_floors: Optional[int] = Field(None, ge=0, le=10, description="地下层数")
    building_area: Optional[float] = Field(None, gt=0, description="建筑面积")
    
    # 危险因素
    hazmat: bool = Field(False, description="是否涉及危化品")
    hazmat_types: List[str] = Field(default_factory=list, description="危化品类型")
    explosion_risk: bool = Field(False, description="是否有爆炸风险")
    collapse_risk: bool = Field(False, description="是否有倒塌风险")
    
    # 人员信息
    trapped_people: Optional[int] = Field(None, ge=0, description="被困人数")
    evacuated_people: Optional[int] = Field(None, ge=0, description="已疏散人数")
    casualties: Optional[int] = Field(None, ge=0, description="伤亡人数")
    
    # 环境因素
    weather_conditions: Optional[str] = Field(None, description="天气条件")
    wind_speed: Optional[float] = Field(None, ge=0, description="风速 (m/s)")
    wind_direction: Optional[str] = Field(None, description="风向")
    temperature: Optional[float] = Field(None, description="温度 (摄氏度)")
    
    # 报警信息
    caller_name: Optional[str] = Field(None, description="报警人姓名")
    caller_phone: Optional[str] = Field(None, description="报警人电话")
    
    # 附加信息
    images: List[str] = Field(default_factory=list, description="现场图片URL")
    videos: List[str] = Field(default_factory=list, description="现场视频URL")
    additional_info: Dict[str, Any] = Field(default_factory=dict, description="附加信息")
    
    @validator("location")
    def validate_location(cls, v):
        if v is not None:
            lng, lat = v
            if not (-180 <= lng <= 180):
                raise ValueError("经度必须在 -180 到 180 之间")
            if not (-90 <= lat <= 90):
                raise ValueError("纬度必须在 -90 到 90 之间")
        return v
    
    @validator("caller_phone")
    def validate_phone(cls, v):
        if v is not None and not v.replace("-", "").replace(" ", "").isdigit():
            raise ValueError("电话号码格式不正确")
        return v
    
    def to_event_context(self, event_id: str) -> EventContext:
        """转换为 EventContext"""
        return EventContext(
            event_id=event_id,
            location=self.location or (0.0, 0.0),  # 如果没有坐标，后续地理编码
            address=self.address,
            level=self.level,
            event_type=self.event_type,
            description=self.description,
            timestamp=datetime.now(),
            building_type=self.building_type,
            floors=self.floors,
            basement_floors=self.basement_floors,
            building_area=self.building_area,
            hazmat=self.hazmat,
            hazmat_types=self.hazmat_types,
            explosion_risk=self.explosion_risk,
            collapse_risk=self.collapse_risk,
            trapped_people=self.trapped_people,
            evacuated_people=self.evacuated_people,
            casualties=self.casualties,
            weather_conditions=self.weather_conditions,
            wind_speed=self.wind_speed,
            wind_direction=self.wind_direction,
            temperature=self.temperature,
            caller_name=self.caller_name,
            caller_phone=self.caller_phone,
            call_time=datetime.now(),
            images=self.images,
            videos=self.videos,
            additional_info=self.additional_info
        )
