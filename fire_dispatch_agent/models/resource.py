"""
资源模型定义

定义救援站、消火栓等资源的数据结构
"""

from typing import Optional, List, Dict, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field


class StationType(Enum):
    """救援站类型"""
    RESCUE_STATION = 1  # 消防救援站
    FULLTIME_STATION = 2  # 专职消防站
    LOGISTICS_STATION = 3  # 战勤保障站


class ResourceStatus(Enum):
    """资源状态"""
    AVAILABLE = "available"  # 可用
    BUSY = "busy"  # 忙碌
    MAINTENANCE = "maintenance"  # 维护中
    OFFLINE = "offline"  # 离线
    RESERVED = "reserved"  # 预留


class VehicleType(Enum):
    """车辆类型"""
    FIRE_TRUCK = "消防车"
    LADDER_TRUCK = "云梯车"
    RESCUE_TRUCK = "抢险救援车"
    WATER_TANKER = "水罐车"
    FOAM_TRUCK = "泡沫车"
    COMMAND_VEHICLE = "指挥车"
    AMBULANCE = "救护车"
    OTHER = "其他"


class HydrantType(Enum):
    """消火栓类型"""
    FIRE_HYDRANT = 3  # 消火栓
    FIRE_CRANE = 4   # 消防水鹤


@dataclass
class ResourceCapacity:
    """资源能力"""
    personnel_count: int = 0  # 人员数量
    vehicles: List[Dict[str, Any]] = field(default_factory=list)  # 车辆列表
    water_capacity: Optional[float] = None  # 载水量 (升)
    foam_capacity: Optional[float] = None   # 泡沫容量 (升)
    ladder_height: Optional[float] = None   # 云梯高度 (米)
    equipment: List[str] = field(default_factory=list)  # 装备列表
    
    def get_total_vehicles(self) -> int:
        """获取车辆总数"""
        return len(self.vehicles)
    
    def get_vehicle_types(self) -> List[str]:
        """获取车辆类型列表"""
        return [v.get("type", "未知") for v in self.vehicles]
    
    def has_vehicle_type(self, vehicle_type: str) -> bool:
        """检查是否有指定类型车辆"""
        return vehicle_type in self.get_vehicle_types()


@dataclass
class ResourceUnit:
    """资源单元"""
    id: str
    name: str
    type: str  # 'rescue_station', 'fulltime_station', 'hydrant', 'workstation'
    location: Tuple[float, float]  # (lng, lat)
    address: Optional[str] = None
    dadui: str = ""  # 所属大队
    
    # 状态信息
    status: ResourceStatus = ResourceStatus.AVAILABLE
    last_updated: datetime = field(default_factory=datetime.now)
    
    # 能力信息
    capacity: Optional[ResourceCapacity] = None
    
    # 联系信息
    phone: Optional[str] = None
    contact_person: Optional[str] = None
    
    # 附加信息
    description: Optional[str] = None
    images: List[str] = field(default_factory=list)
    additional_info: Dict[str, Any] = field(default_factory=dict)
    
    def distance_to(self, target_location: Tuple[float, float]) -> float:
        """计算到目标位置的直线距离 (公里)"""
        from ..utils.geo_utils import calculate_distance
        return calculate_distance(self.location, target_location)
    
    def is_available(self) -> bool:
        """检查是否可用"""
        return self.status == ResourceStatus.AVAILABLE
    
    def get_response_capability(self) -> Dict[str, Any]:
        """获取响应能力评估"""
        if not self.capacity:
            return {"score": 0, "details": "无能力信息"}
        
        score = 0
        details = []
        
        # 人员评分
        if self.capacity.personnel_count > 0:
            personnel_score = min(self.capacity.personnel_count / 20, 1.0) * 30
            score += personnel_score
            details.append(f"人员: {self.capacity.personnel_count}人")
        
        # 车辆评分
        vehicle_count = self.capacity.get_total_vehicles()
        if vehicle_count > 0:
            vehicle_score = min(vehicle_count / 5, 1.0) * 40
            score += vehicle_score
            details.append(f"车辆: {vehicle_count}台")
        
        # 特殊装备评分
        if self.capacity.water_capacity:
            score += 15
            details.append(f"载水: {self.capacity.water_capacity}升")
        
        if self.capacity.ladder_height:
            score += 15
            details.append(f"云梯: {self.capacity.ladder_height}米")
        
        return {
            "score": min(score, 100),
            "details": "; ".join(details),
            "personnel": self.capacity.personnel_count,
            "vehicles": vehicle_count
        }


@dataclass
class StationInfo:
    """救援站详细信息"""
    resource_unit: ResourceUnit
    station_type: StationType
    jurisdiction_area: Optional[str] = None  # 辖区描述
    coverage_radius: float = 10.0  # 覆盖半径 (公里)
    
    # 历史数据
    response_times: List[float] = field(default_factory=list)  # 历史响应时间
    success_rate: float = 0.95  # 成功率
    
    def get_average_response_time(self) -> float:
        """获取平均响应时间"""
        if not self.response_times:
            return 0.0
        return sum(self.response_times) / len(self.response_times)
    
    def get_reliability_score(self) -> float:
        """获取可靠性评分 (0-1)"""
        base_score = self.success_rate
        
        # 响应时间调整
        avg_response = self.get_average_response_time()
        if avg_response > 0:
            time_factor = max(0, 1 - (avg_response - 300) / 600)  # 5分钟为基准
            base_score *= time_factor
        
        return max(0, min(1, base_score))


class ResourceQuery(BaseModel):
    """资源查询请求"""
    
    location: Tuple[float, float] = Field(..., description="查询位置 (经度, 纬度)")
    radius: float = Field(10.0, ge=1, le=50, description="搜索半径 (公里)")
    resource_types: List[str] = Field(
        default_factory=lambda: ["rescue_station", "fulltime_station"], 
        description="资源类型"
    )
    max_results: int = Field(20, ge=1, le=100, description="最大结果数")
    include_unavailable: bool = Field(False, description="是否包含不可用资源")
    dadui_filter: Optional[str] = Field(None, description="大队过滤")


class ResourceResponse(BaseModel):
    """资源查询响应"""
    
    resources: List[Dict[str, Any]] = Field(..., description="资源列表")
    total_count: int = Field(..., description="总数量")
    search_radius: float = Field(..., description="实际搜索半径")
    center_location: Tuple[float, float] = Field(..., description="搜索中心")
    dadui_distribution: Dict[str, int] = Field(..., description="大队分布统计")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
