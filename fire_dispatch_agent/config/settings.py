"""
应用配置管理

使用 Pydantic Settings 管理环境变量和配置
支持多环境配置、类型验证和默认值设置
"""

from typing import List, Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class DatabaseSettings(BaseSettings):
    """数据库配置"""
    
    url: str = Field(..., alias="DATABASE_URL")
    pool_size: int = Field(10, alias="DB_POOL_SIZE")
    max_overflow: int = Field(20, alias="DB_MAX_OVERFLOW")
    pool_timeout: int = Field(30, alias="DB_POOL_TIMEOUT")
    pool_recycle: int = Field(3600, alias="DB_POOL_RECYCLE")
    retry_attempts: int = Field(3, alias="DB_RETRY_ATTEMPTS")
    retry_delay: int = Field(1, alias="DB_RETRY_DELAY")


class RedisSettings(BaseSettings):
    """Redis 缓存配置"""
    
    url: str = Field("redis://localhost:6379/0", alias="REDIS_URL")
    password: Optional[str] = Field(None, alias="REDIS_PASSWORD")
    db: int = Field(0, alias="REDIS_DB")
    max_connections: int = Field(10, alias="REDIS_MAX_CONNECTIONS")
    socket_timeout: int = Field(5, alias="REDIS_SOCKET_TIMEOUT")
    socket_connect_timeout: int = Field(5, alias="REDIS_SOCKET_CONNECT_TIMEOUT")
    
    # 缓存 TTL 配置
    default_ttl: int = Field(300, alias="CACHE_DEFAULT_TTL")
    eta_ttl: int = Field(300, alias="CACHE_ETA_TTL")
    resource_status_ttl: int = Field(60, alias="CACHE_RESOURCE_STATUS_TTL")
    dispatch_plan_ttl: int = Field(1800, alias="CACHE_DISPATCH_PLAN_TTL")


class LLMSettings(BaseSettings):
    """LLM 配置"""
    
    openai_api_key: str = Field(..., alias="OPENAI_API_KEY")
    openai_model: str = Field("gpt-4o", alias="OPENAI_MODEL")
    openai_temperature: float = Field(0.1, alias="OPENAI_TEMPERATURE")
    openai_max_tokens: int = Field(2000, alias="OPENAI_MAX_TOKENS")
    openai_timeout: int = Field(30, alias="OPENAI_TIMEOUT")
    
    langgraph_api_url: Optional[str] = Field(None, alias="LANGGRAPH_API_URL")
    langgraph_api_key: Optional[str] = Field(None, alias="LANGGRAPH_API_KEY")


class AmapSettings(BaseSettings):
    """高德地图 API 配置"""
    
    api_key: str = Field(..., alias="AMAP_API_KEY")
    base_url: str = Field("https://restapi.amap.com", alias="AMAP_BASE_URL")
    timeout: int = Field(10, alias="AMAP_TIMEOUT")
    retry_attempts: int = Field(3, alias="AMAP_RETRY_ATTEMPTS")
    
    # API 端点配置
    geocoding_url: str = Field("/v3/geocode/geo", alias="AMAP_GEOCODING_URL")
    regeo_url: str = Field("/v3/geocode/regeo", alias="AMAP_REGEO_URL")
    direction_url: str = Field("/v3/direction/driving", alias="AMAP_DIRECTION_URL")
    traffic_url: str = Field("/v3/traffic/status/rectangle", alias="AMAP_TRAFFIC_URL")


class BusinessSettings(BaseSettings):
    """业务配置"""
    
    # 搜索配置
    default_search_radius: float = Field(10.0, alias="DEFAULT_SEARCH_RADIUS")
    max_search_radius: float = Field(50.0, alias="MAX_SEARCH_RADIUS")
    max_nearby_stations: int = Field(20, alias="MAX_NEARBY_STATIONS")
    max_nearby_hydrants: int = Field(50, alias="MAX_NEARBY_HYDRANTS")
    
    # 调度配置
    max_dispatch_plans: int = Field(3, alias="MAX_DISPATCH_PLANS")
    min_dispatch_plans: int = Field(1, alias="MIN_DISPATCH_PLANS")
    enable_backup_plans: bool = Field(True, alias="ENABLE_BACKUP_PLANS")
    jurisdiction_priority: bool = Field(True, alias="JURISDICTION_PRIORITY")
    
    # 路径规划配置
    enable_traffic_consideration: bool = Field(True, alias="ENABLE_TRAFFIC_CONSIDERATION")
    route_alternatives: int = Field(3, alias="ROUTE_ALTERNATIVES")
    max_route_distance: float = Field(100.0, alias="MAX_ROUTE_DISTANCE")
    
    # 通知配置
    enable_websocket_notifications: bool = Field(True, alias="ENABLE_WEBSOCKET_NOTIFICATIONS")
    websocket_heartbeat_interval: int = Field(30, alias="WEBSOCKET_HEARTBEAT_INTERVAL")
    max_websocket_connections: int = Field(1000, alias="MAX_WEBSOCKET_CONNECTIONS")


class APISettings(BaseSettings):
    """API 服务配置"""
    
    host: str = Field("0.0.0.0", alias="API_HOST")
    port: int = Field(8000, alias="API_PORT")
    workers: int = Field(4, alias="API_WORKERS")
    reload: bool = Field(False, alias="API_RELOAD")
    
    # CORS 配置
    cors_origins: List[str] = Field(
        ["http://localhost:3000", "http://localhost:8080"], 
        alias="CORS_ORIGINS"
    )
    cors_credentials: bool = Field(True, alias="CORS_CREDENTIALS")
    cors_methods: List[str] = Field(
        ["GET", "POST", "PUT", "DELETE", "OPTIONS"], 
        alias="CORS_METHODS"
    )
    cors_headers: str = Field("*", alias="CORS_HEADERS")
    
    # 限流配置
    rate_limit_requests: int = Field(100, alias="RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(60, alias="RATE_LIMIT_WINDOW")
    
    @validator("cors_origins", pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("cors_methods", pre=True)
    def parse_cors_methods(cls, v):
        if isinstance(v, str):
            return [method.strip() for method in v.split(",")]
        return v


class SecuritySettings(BaseSettings):
    """安全配置"""
    
    secret_key: str = Field(..., alias="SECRET_KEY")
    jwt_secret_key: str = Field(..., alias="JWT_SECRET_KEY")
    jwt_algorithm: str = Field("HS256", alias="JWT_ALGORITHM")
    jwt_access_token_expire_minutes: int = Field(30, alias="JWT_ACCESS_TOKEN_EXPIRE_MINUTES")
    jwt_refresh_token_expire_days: int = Field(7, alias="JWT_REFRESH_TOKEN_EXPIRE_DAYS")
    
    api_key_header: str = Field("X-API-Key", alias="API_KEY_HEADER")
    admin_api_key: Optional[str] = Field(None, alias="ADMIN_API_KEY")


class MonitoringSettings(BaseSettings):
    """监控与日志配置"""
    
    log_level: str = Field("INFO", alias="LOG_LEVEL")
    log_format: str = Field("json", alias="LOG_FORMAT")
    log_file_path: str = Field("./logs/fire_dispatch.log", alias="LOG_FILE_PATH")
    log_max_size: str = Field("100MB", alias="LOG_MAX_SIZE")
    log_backup_count: int = Field(5, alias="LOG_BACKUP_COUNT")
    
    enable_metrics: bool = Field(True, alias="ENABLE_METRICS")
    metrics_port: int = Field(9090, alias="METRICS_PORT")
    health_check_interval: int = Field(30, alias="HEALTH_CHECK_INTERVAL")
    
    sentry_dsn: Optional[str] = Field(None, alias="SENTRY_DSN")
    sentry_environment: str = Field("development", alias="SENTRY_ENVIRONMENT")
    sentry_traces_sample_rate: float = Field(0.1, alias="SENTRY_TRACES_SAMPLE_RATE")


class Settings(BaseSettings):
    """主配置类"""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    # 应用基础配置
    app_name: str = Field("Fire Dispatch Agent", alias="APP_NAME")
    app_version: str = Field("0.1.0", alias="APP_VERSION")
    debug: bool = Field(False, alias="DEBUG")
    
    # 子配置
    database: DatabaseSettings = DatabaseSettings()
    redis: RedisSettings = RedisSettings()
    llm: LLMSettings = LLMSettings()
    amap: AmapSettings = AmapSettings()
    business: BusinessSettings = BusinessSettings()
    api: APISettings = APISettings()
    security: SecuritySettings = SecuritySettings()
    monitoring: MonitoringSettings = MonitoringSettings()
    
    # 开发与测试配置
    dev_mode: bool = Field(False, alias="DEV_MODE")
    mock_external_apis: bool = Field(False, alias="MOCK_EXTERNAL_APIS")
    enable_api_docs: bool = Field(True, alias="ENABLE_API_DOCS")
    enable_profiling: bool = Field(False, alias="ENABLE_PROFILING")
    
    # 部署配置
    deployment_environment: str = Field("development", alias="DEPLOYMENT_ENVIRONMENT")
    
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.deployment_environment == "development"
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.deployment_environment == "production"


# 全局配置实例
settings = Settings()
