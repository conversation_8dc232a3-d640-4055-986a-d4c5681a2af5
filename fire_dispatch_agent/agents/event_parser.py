"""
事件解析 Agent

负责解析和标准化事件信息，包括地理编码、事件分类、优先级评估等
"""

import time
from typing import Dict, Any, Optional
from datetime import datetime

from langgraph.graph import StateGraph, END
from langchain_core.messages import AIMessage
from langchain_core.tools import tool

from ..utils.state import FireDispatchState, AgentStatus, ProcessingStage
from ..models.event import EventContext, EventLevel, EventType, BuildingType
from ..config.settings import settings


@tool
async def geocode_address(address: str) -> Optional[Dict[str, Any]]:
    """
    地址地理编码 - 调用高德地图API

    Args:
        address: 地址字符串

    Returns:
        地理编码结果 {"lng": float, "lat": float, "formatted_address": str}
    """
    try:
        from ..utils.amap_client import AmapClient
        amap_client = AmapClient()
        result = await amap_client.geocode(address)
        return result
    except Exception as e:
        # 降级处理：返回模拟数据
        if "大连" in address and "西岗" in address:
            return {
                "lng": 121.612341,
                "lat": 38.917218,
                "formatted_address": "大连市西岗区北京街57号",
                "confidence": 0.95,
                "level": "门牌号"
            }

        # 默认返回大连市中心
        return {
            "lng": 121.614682,
            "lat": 38.914003,
            "formatted_address": address,
            "confidence": 0.5,
            "level": "城市"
        }


@tool
async def classify_event_type(description: str, building_info: Optional[str] = None) -> Dict[str, Any]:
    """
    事件类型分类
    
    Args:
        description: 事件描述
        building_info: 建筑信息
    
    Returns:
        分类结果
    """
    # 简单的关键词匹配分类
    description_lower = description.lower()
    
    # 事件类型判断
    if any(keyword in description_lower for keyword in ["火灾", "着火", "起火", "燃烧"]):
        event_type = EventType.FIRE
    elif any(keyword in description_lower for keyword in ["救援", "被困", "坍塌", "掉落"]):
        event_type = EventType.RESCUE
    elif any(keyword in description_lower for keyword in ["危化品", "化学品", "泄漏", "爆炸"]):
        event_type = EventType.HAZMAT
    elif any(keyword in description_lower for keyword in ["车祸", "交通", "撞车"]):
        event_type = EventType.TRAFFIC
    elif any(keyword in description_lower for keyword in ["急救", "心脏病", "中毒"]):
        event_type = EventType.MEDICAL
    else:
        event_type = EventType.OTHER
    
    # 建筑类型判断
    building_type = None
    if building_info:
        building_lower = building_info.lower()
        if any(keyword in building_lower for keyword in ["住宅", "小区", "居民楼"]):
            building_type = BuildingType.RESIDENTIAL
        elif any(keyword in building_lower for keyword in ["商场", "商业", "店铺"]):
            building_type = BuildingType.COMMERCIAL
        elif any(keyword in building_lower for keyword in ["工厂", "厂房", "工业"]):
            building_type = BuildingType.INDUSTRIAL
        elif any(keyword in building_lower for keyword in ["办公", "写字楼", "大厦"]):
            building_type = BuildingType.OFFICE
        elif any(keyword in building_lower for keyword in ["医院", "诊所"]):
            building_type = BuildingType.HOSPITAL
        elif any(keyword in building_lower for keyword in ["学校", "幼儿园", "教学楼"]):
            building_type = BuildingType.SCHOOL
        elif any(keyword in building_lower for keyword in ["高层", "摩天楼"]):
            building_type = BuildingType.HIGH_RISE
    
    # 危险因素判断
    hazmat = any(keyword in description_lower for keyword in ["危化品", "化学品", "有毒", "易燃", "易爆"])
    explosion_risk = any(keyword in description_lower for keyword in ["爆炸", "易爆", "气体泄漏"])
    collapse_risk = any(keyword in description_lower for keyword in ["坍塌", "倒塌", "结构损坏"])
    
    return {
        "event_type": event_type,
        "building_type": building_type,
        "hazmat": hazmat,
        "explosion_risk": explosion_risk,
        "collapse_risk": collapse_risk,
        "confidence": 0.8
    }


@tool
async def assess_event_priority(event_context: Dict[str, Any]) -> Dict[str, Any]:
    """
    事件优先级评估
    
    Args:
        event_context: 事件上下文信息
    
    Returns:
        优先级评估结果
    """
    priority_score = 1
    factors = []
    
    # 事件等级基础分数
    level = event_context.get("level")
    if level == EventLevel.CRITICAL:
        priority_score += 4
        factors.append("特别重大事件")
    elif level == EventLevel.SERIOUS:
        priority_score += 3
        factors.append("重大事件")
    elif level == EventLevel.MAJOR:
        priority_score += 2
        factors.append("较大事件")
    
    # 人员因素
    trapped_people = event_context.get("trapped_people", 0)
    if trapped_people > 0:
        priority_score += min(trapped_people // 5, 3)
        factors.append(f"被困人员{trapped_people}人")
    
    casualties = event_context.get("casualties", 0)
    if casualties > 0:
        priority_score += min(casualties // 2, 4)
        factors.append(f"伤亡人员{casualties}人")
    
    # 危险因素
    if event_context.get("hazmat", False):
        priority_score += 2
        factors.append("涉及危化品")
    
    if event_context.get("explosion_risk", False):
        priority_score += 2
        factors.append("爆炸风险")
    
    if event_context.get("collapse_risk", False):
        priority_score += 1
        factors.append("倒塌风险")
    
    # 建筑类型
    building_type = event_context.get("building_type")
    if building_type in [BuildingType.HOSPITAL, BuildingType.SCHOOL]:
        priority_score += 1
        factors.append("重要建筑")
    elif building_type == BuildingType.HIGH_RISE:
        priority_score += 1
        factors.append("高层建筑")
    
    # 环境因素
    wind_speed = event_context.get("wind_speed", 0)
    if wind_speed > 10:  # 风速超过10m/s
        priority_score += 1
        factors.append("大风天气")
    
    priority_score = min(priority_score, 10)  # 最高10分
    
    return {
        "priority_score": priority_score,
        "priority_factors": factors,
        "is_high_priority": priority_score >= 7,
        "recommended_response_level": "一级响应" if priority_score >= 8 else "二级响应" if priority_score >= 6 else "三级响应"
    }


async def parse_event_node(state: FireDispatchState) -> FireDispatchState:
    """事件解析节点"""
    start_time = time.time()
    
    try:
        state.set_stage(ProcessingStage.EVENT_PARSING)
        state.current_agent = "event_parser"
        
        if not state.event:
            error_msg = "缺少事件信息"
            state.add_error(error_msg)
            state.add_agent_result("event_parser", AgentStatus.FAILED, error=error_msg)
            return state
        
        # 地理编码 (如果缺少坐标)
        if not state.event.location or state.event.location == (0.0, 0.0):
            geocode_result = await geocode_address(state.event.address)
            if geocode_result:
                state.event.location = (geocode_result["lng"], geocode_result["lat"])
                state.event.address = geocode_result["formatted_address"]
                
                if geocode_result["confidence"] < 0.7:
                    state.add_warning(f"地理编码置信度较低: {geocode_result['confidence']}")
        
        # 事件分类
        classification = await classify_event_type(
            state.event.description,
            state.event.additional_info.get("building_info")
        )
        
        # 更新事件信息
        if not state.event.event_type or state.event.event_type == EventType.OTHER:
            state.event.event_type = classification["event_type"]
        
        if not state.event.building_type:
            state.event.building_type = classification["building_type"]
        
        # 更新危险因素
        state.event.hazmat = state.event.hazmat or classification["hazmat"]
        state.event.explosion_risk = state.event.explosion_risk or classification["explosion_risk"]
        state.event.collapse_risk = state.event.collapse_risk or classification["collapse_risk"]
        
        # 优先级评估
        priority_assessment = await assess_event_priority(state.event.__dict__)
        state.event_priority = priority_assessment["priority_score"]
        state.event_tags = priority_assessment["priority_factors"]
        
        # 添加处理消息
        state.messages.append(AIMessage(
            content=f"事件解析完成: {state.event.address}, "
                   f"类型: {state.event.event_type.value}, "
                   f"等级: {state.event.level.value}, "
                   f"优先级: {state.event_priority}/10"
        ))
        
        # 记录成功结果
        execution_time = time.time() - start_time
        state.add_agent_result(
            "event_parser", 
            AgentStatus.COMPLETED,
            result={
                "location": state.event.location,
                "event_type": state.event.event_type.value,
                "building_type": state.event.building_type.value if state.event.building_type else None,
                "priority_score": state.event_priority,
                "priority_factors": state.event_tags,
                "classification_confidence": classification["confidence"]
            },
            execution_time=execution_time
        )
        
        state.next_agent = "spatial_analyzer"
        
    except Exception as e:
        error_msg = f"事件解析失败: {str(e)}"
        state.add_error(error_msg)
        state.add_agent_result("event_parser", AgentStatus.FAILED, error=error_msg)
    
    return state


def create_event_parser_agent() -> StateGraph:
    """创建事件解析 Agent"""
    
    # 创建状态图
    workflow = StateGraph(FireDispatchState)
    
    # 添加节点
    workflow.add_node("parse_event", parse_event_node)
    
    # 设置入口点和结束点
    workflow.set_entry_point("parse_event")
    workflow.add_edge("parse_event", END)
    
    return workflow.compile()
