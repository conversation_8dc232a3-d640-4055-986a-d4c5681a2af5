"""
路径评估 Agent

负责路径规划和评估，包括ETA计算、路况分析、备选路径等
"""

import time
from typing import Dict, Any, List, Tuple, Optional

from langgraph.graph import StateGraph, END
from langchain_core.messages import AIMessage
from langchain_core.tools import tool

from ..utils.state import FireDispatchState, AgentStatus, ProcessingStage, RouteEvaluationResult
from ..models.dispatch import RouteInfo, RouteType
from ..config.settings import settings


@tool
async def calculate_route_eta(
    start_location: Tuple[float, float],
    end_location: Tuple[float, float],
    route_type: str = "fastest"
) -> Dict[str, Any]:
    """
    计算路径和预计到达时间
    
    Args:
        start_location: 起点 (经度, 纬度)
        end_location: 终点 (经度, 纬度)
        route_type: 路径类型 (fastest, shortest, avoid_traffic)
    
    Returns:
        路径信息
    """
    try:
        from ..utils.amap_client import AmapClient
        amap_client = AmapClient()
        
        route_result = await amap_client.calculate_route(
            start_location,
            end_location,
            route_type
        )
        
        return route_result
        
    except Exception as e:
        # 降级处理：基于直线距离估算
        from ..utils.geo_utils import calculate_distance
        
        distance_km = calculate_distance(start_location, end_location)
        
        # 简单估算：城市道路平均速度30km/h，加上20%绕行系数
        estimated_distance = distance_km * 1.2
        estimated_time = int(estimated_distance / 30 * 60)  # 转换为分钟
        
        return {
            "distance_km": round(estimated_distance, 2),
            "duration_minutes": max(estimated_time, 5),  # 最少5分钟
            "route_points": [start_location, end_location],
            "traffic_status": "unknown",
            "traffic_delay_minutes": 0,
            "instructions": [f"从起点到终点，预计{estimated_time}分钟"],
            "road_names": ["未知道路"],
            "risk_level": "low",
            "risk_factors": [],
            "method": "fallback_estimation"
        }


@tool
async def get_traffic_conditions(route_points: List[Tuple[float, float]]) -> Dict[str, Any]:
    """
    获取路况信息
    
    Args:
        route_points: 路径点列表
    
    Returns:
        路况信息
    """
    try:
        from ..utils.amap_client import AmapClient
        amap_client = AmapClient()
        
        traffic_result = await amap_client.get_traffic_status(route_points)
        return traffic_result
        
    except Exception as e:
        # 降级处理：返回默认路况
        return {
            "overall_status": "畅通",
            "congestion_level": 1,  # 1-5级，1为畅通
            "delay_minutes": 0,
            "congested_segments": [],
            "alternative_suggested": False,
            "method": "fallback"
        }


@tool
async def generate_alternative_routes(
    start_location: Tuple[float, float],
    end_location: Tuple[float, float],
    primary_route: Dict[str, Any],
    max_alternatives: int = 2
) -> List[Dict[str, Any]]:
    """
    生成备选路径
    
    Args:
        start_location: 起点
        end_location: 终点
        primary_route: 主路径
        max_alternatives: 最大备选路径数
    
    Returns:
        备选路径列表
    """
    alternatives = []
    
    try:
        # 尝试不同的路径类型
        route_types = ["shortest", "avoid_traffic"]
        
        for route_type in route_types[:max_alternatives]:
            if route_type != primary_route.get("route_type", "fastest"):
                alt_route = await calculate_route_eta(start_location, end_location, route_type)
                alt_route["route_type"] = route_type
                alt_route["is_alternative"] = True
                alternatives.append(alt_route)
        
    except Exception as e:
        # 如果无法生成备选路径，创建简单的备选方案
        from ..utils.geo_utils import calculate_distance
        
        distance_km = calculate_distance(start_location, end_location)
        
        # 备选方案1：较慢但更安全的路径
        alt1 = {
            "distance_km": round(distance_km * 1.4, 2),
            "duration_minutes": int(distance_km * 1.4 / 25 * 60),  # 25km/h
            "route_points": [start_location, end_location],
            "traffic_status": "畅通",
            "traffic_delay_minutes": 0,
            "route_type": "safe",
            "is_alternative": True,
            "risk_level": "low",
            "risk_factors": [],
            "instructions": ["备选安全路径"]
        }
        alternatives.append(alt1)
    
    return alternatives


async def evaluate_route_risks(route_info: Dict[str, Any], event_context: Dict[str, Any]) -> Dict[str, Any]:
    """评估路径风险"""
    risk_factors = []
    risk_level = "low"
    
    # 距离风险
    distance_km = route_info.get("distance_km", 0)
    if distance_km > 20:
        risk_factors.append("距离较远")
        risk_level = "medium"
    
    # 时间风险
    duration_minutes = route_info.get("duration_minutes", 0)
    if duration_minutes > 30:
        risk_factors.append("耗时较长")
        risk_level = "medium"
    
    # 交通风险
    traffic_delay = route_info.get("traffic_delay_minutes", 0)
    if traffic_delay > 10:
        risk_factors.append("交通拥堵")
        risk_level = "high" if traffic_delay > 20 else "medium"
    
    # 天气风险
    weather = event_context.get("weather_conditions", "")
    if weather and any(condition in weather.lower() for condition in ["雨", "雪", "雾", "冰"]):
        risk_factors.append("恶劣天气")
        risk_level = "high" if risk_level == "medium" else "medium"
    
    # 时间段风险
    from datetime import datetime
    current_hour = datetime.now().hour
    if 7 <= current_hour <= 9 or 17 <= current_hour <= 19:
        risk_factors.append("交通高峰期")
        risk_level = "medium" if risk_level == "low" else risk_level
    
    return {
        "risk_level": risk_level,
        "risk_factors": risk_factors,
        "risk_score": len(risk_factors),
        "recommendations": generate_risk_recommendations(risk_factors)
    }


def generate_risk_recommendations(risk_factors: List[str]) -> List[str]:
    """生成风险建议"""
    recommendations = []
    
    if "距离较远" in risk_factors:
        recommendations.append("考虑增派就近支援")
    
    if "耗时较长" in risk_factors:
        recommendations.append("提前通知现场准备")
    
    if "交通拥堵" in risk_factors:
        recommendations.append("启用应急通道")
        recommendations.append("协调交警疏导")
    
    if "恶劣天气" in risk_factors:
        recommendations.append("降低行驶速度")
        recommendations.append("增加安全距离")
    
    if "交通高峰期" in risk_factors:
        recommendations.append("避开主要干道")
        recommendations.append("使用警报器开道")
    
    return recommendations


async def route_evaluation_node(state: FireDispatchState) -> FireDispatchState:
    """路径评估节点"""
    start_time = time.time()
    
    try:
        state.set_stage(ProcessingStage.ROUTE_EVALUATION)
        state.current_agent = "route_evaluator"
        
        if not state.dispatch_planning or not state.dispatch_planning.selected_plan:
            error_msg = "缺少调度方案"
            state.add_error(error_msg)
            state.add_agent_result("route_evaluator", AgentStatus.FAILED, error=error_msg)
            return state
        
        selected_plan = state.dispatch_planning.selected_plan
        event_location = state.event.location
        event_context = state.event.__dict__ if state.event else {}
        
        # 为每个主力救援站计算路径
        routes = []
        for station in selected_plan.primary_stations:
            station_location = station.location
            
            # 计算主路径
            primary_route_data = await calculate_route_eta(
                station_location,
                event_location,
                "fastest"
            )
            
            # 创建 RouteInfo 对象
            route_id = f"route_{station.id}_{int(time.time())}"
            primary_route = RouteInfo(
                route_id=route_id,
                start_location=station_location,
                end_location=event_location,
                route_type=RouteType.FASTEST,
                distance_km=primary_route_data["distance_km"],
                duration_minutes=primary_route_data["duration_minutes"],
                route_points=primary_route_data.get("route_points", []),
                traffic_status=primary_route_data.get("traffic_status", "unknown"),
                traffic_delay_minutes=primary_route_data.get("traffic_delay_minutes", 0),
                instructions=primary_route_data.get("instructions", []),
                road_names=primary_route_data.get("road_names", [])
            )
            
            # 评估路径风险
            risk_assessment = await evaluate_route_risks(primary_route_data, event_context)
            primary_route.risk_level = risk_assessment["risk_level"]
            primary_route.risk_factors = risk_assessment["risk_factors"]
            
            routes.append(primary_route)
        
        # 生成备选路径 (为最优站点)
        alternative_routes = []
        if routes:
            best_route = min(routes, key=lambda r: r.get_total_time())
            
            alt_routes_data = await generate_alternative_routes(
                best_route.start_location,
                best_route.end_location,
                {
                    "route_type": best_route.route_type.value,
                    "duration_minutes": best_route.duration_minutes
                }
            )
            
            for i, alt_data in enumerate(alt_routes_data):
                alt_route_id = f"alt_route_{i}_{int(time.time())}"
                alt_route = RouteInfo(
                    route_id=alt_route_id,
                    start_location=best_route.start_location,
                    end_location=best_route.end_location,
                    route_type=RouteType.SHORTEST if alt_data.get("route_type") == "shortest" else RouteType.AVOID_TRAFFIC,
                    distance_km=alt_data["distance_km"],
                    duration_minutes=alt_data["duration_minutes"],
                    route_points=alt_data.get("route_points", []),
                    traffic_status=alt_data.get("traffic_status", "unknown"),
                    traffic_delay_minutes=alt_data.get("traffic_delay_minutes", 0),
                    risk_level=alt_data.get("risk_level", "low"),
                    risk_factors=alt_data.get("risk_factors", [])
                )
                alternative_routes.append(alt_route)
        
        # 获取整体路况信息
        traffic_conditions = None
        if routes:
            all_route_points = []
            for route in routes:
                all_route_points.extend(route.route_points)
            
            if all_route_points:
                traffic_conditions = await get_traffic_conditions(all_route_points)
        
        # 更新选中方案的ETA (使用最快路径)
        if routes:
            fastest_route = min(routes, key=lambda r: r.get_total_time())
            selected_plan.eta_minutes = fastest_route.get_total_time()
            selected_plan.routes = routes
        
        # 创建路径评估结果
        state.route_evaluation = RouteEvaluationResult(
            routes=routes,
            alternative_routes=alternative_routes,
            traffic_conditions=traffic_conditions,
            eta_analysis={
                "min_eta": min(r.get_total_time() for r in routes) if routes else 0,
                "max_eta": max(r.get_total_time() for r in routes) if routes else 0,
                "avg_eta": sum(r.get_total_time() for r in routes) / len(routes) if routes else 0,
                "total_routes": len(routes),
                "alternative_routes": len(alternative_routes),
                "traffic_impact": traffic_conditions.get("delay_minutes", 0) if traffic_conditions else 0
            }
        )
        
        # 添加处理消息
        eta_info = f"最快{min(r.get_total_time() for r in routes)}分钟" if routes else "无路径信息"
        state.messages.append(AIMessage(
            content=f"路径评估完成: {len(routes)}条主路径, "
                   f"{len(alternative_routes)}条备选路径, ETA {eta_info}"
        ))
        
        # 记录成功结果
        execution_time = time.time() - start_time
        state.add_agent_result(
            "route_evaluator",
            AgentStatus.COMPLETED,
            result={
                "routes_calculated": len(routes),
                "alternative_routes": len(alternative_routes),
                "min_eta_minutes": min(r.get_total_time() for r in routes) if routes else None,
                "traffic_conditions": traffic_conditions.get("overall_status") if traffic_conditions else "unknown",
                "eta_analysis": state.route_evaluation.eta_analysis
            },
            execution_time=execution_time
        )
        
        state.next_agent = "notification_agent"
        
    except Exception as e:
        error_msg = f"路径评估失败: {str(e)}"
        state.add_error(error_msg)
        state.add_agent_result("route_evaluator", AgentStatus.FAILED, error=error_msg)
    
    return state


def create_route_evaluator_agent() -> StateGraph:
    """创建路径评估 Agent"""
    
    # 创建状态图
    workflow = StateGraph(FireDispatchState)
    
    # 添加节点
    workflow.add_node("route_evaluation", route_evaluation_node)
    
    # 设置入口点和结束点
    workflow.set_entry_point("route_evaluation")
    workflow.add_edge("route_evaluation", END)
    
    return workflow.compile()
