"""
调度规划 Agent

负责生成调度方案，包括资源选择、优先级排序、备选方案等
"""

import time
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime

from langgraph.graph import StateGraph, END
from langchain_core.messages import AIMessage
from langchain_core.tools import tool

from ..utils.state import FireDispatchState, AgentStatus, ProcessingStage, DispatchPlanningResult
from ..models.resource import ResourceUnit, ResourceStatus
from ..models.dispatch import DispatchPlan, DispatchStatus
from ..models.event import EventLevel
from ..config.settings import settings


@tool
async def select_optimal_resources(
    event_context: Dict[str, Any],
    available_stations: List[Dict[str, Any]],
    jurisdiction_dadui: str,
    max_stations: int = 5
) -> Dict[str, Any]:
    """
    选择最优资源组合
    
    Args:
        event_context: 事件上下文
        available_stations: 可用救援站列表
        jurisdiction_dadui: 管辖大队
        max_stations: 最大站点数
    
    Returns:
        资源选择结果
    """
    if not available_stations:
        return {"primary": [], "backup": [], "reasoning": "无可用救援站"}
    
    # 计算每个站点的评分
    scored_stations = []
    for station in available_stations:
        score = calculate_station_score(station, event_context, jurisdiction_dadui)
        scored_stations.append({
            "station": station,
            "score": score["total_score"],
            "score_details": score
        })
    
    # 按评分排序
    scored_stations.sort(key=lambda x: x["score"], reverse=True)
    
    # 选择主力站点
    event_level = event_context.get("level", EventLevel.MINOR)
    required_stations = get_required_stations_count(event_level, event_context)
    
    primary_count = min(required_stations, len(scored_stations), max_stations)
    primary_stations = scored_stations[:primary_count]
    
    # 选择备用站点
    backup_count = min(2, len(scored_stations) - primary_count)
    backup_stations = scored_stations[primary_count:primary_count + backup_count]
    
    # 生成决策理由
    reasoning_parts = []
    reasoning_parts.append(f"根据{event_level.value}事件等级，需要{required_stations}个救援站")
    reasoning_parts.append(f"优先选择{jurisdiction_dadui}辖区内站点")
    reasoning_parts.append(f"综合考虑距离、响应能力、可用性等因素")
    
    if primary_stations:
        best_station = primary_stations[0]
        reasoning_parts.append(f"首选{best_station['station']['name']}(评分:{best_station['score']:.1f})")
    
    return {
        "primary": [s["station"] for s in primary_stations],
        "backup": [s["station"] for s in backup_stations],
        "reasoning": "; ".join(reasoning_parts),
        "selection_details": {
            "required_stations": required_stations,
            "primary_count": len(primary_stations),
            "backup_count": len(backup_stations),
            "total_evaluated": len(scored_stations)
        }
    }


def calculate_station_score(station: Dict[str, Any], event_context: Dict[str, Any], jurisdiction_dadui: str) -> Dict[str, Any]:
    """计算救援站评分"""
    score_details = {
        "distance_score": 0,
        "jurisdiction_score": 0,
        "capacity_score": 0,
        "availability_score": 0,
        "total_score": 0
    }
    
    # 距离评分 (40分) - 距离越近分数越高
    distance_km = station.get("distance_km", float('inf'))
    if distance_km < float('inf'):
        # 5公里内满分，每增加1公里减2分
        distance_score = max(0, 40 - (distance_km - 5) * 2) if distance_km > 5 else 40
        score_details["distance_score"] = min(40, max(0, distance_score))
    
    # 辖区评分 (20分) - 本辖区优先
    if station.get("dadui") == jurisdiction_dadui:
        score_details["jurisdiction_score"] = 20
    elif station.get("dadui"):  # 其他辖区
        score_details["jurisdiction_score"] = 10
    
    # 能力评分 (30分) - 人员和车辆数量
    capacity = station.get("capacity", {})
    personnel_count = capacity.get("personnel_count", 0)
    vehicles = capacity.get("vehicles", [])
    vehicle_count = sum(v.get("count", 1) for v in vehicles)
    
    # 人员评分 (15分)
    personnel_score = min(15, personnel_count / 2)  # 30人满分
    
    # 车辆评分 (15分)
    vehicle_score = min(15, vehicle_count * 3)  # 5台车满分
    
    score_details["capacity_score"] = personnel_score + vehicle_score
    
    # 可用性评分 (10分)
    status = station.get("status", "available")
    if status == "available":
        score_details["availability_score"] = 10
    elif status == "busy":
        score_details["availability_score"] = 5
    else:
        score_details["availability_score"] = 0
    
    # 总分
    score_details["total_score"] = sum([
        score_details["distance_score"],
        score_details["jurisdiction_score"],
        score_details["capacity_score"],
        score_details["availability_score"]
    ])
    
    return score_details


def get_required_stations_count(event_level: EventLevel, event_context: Dict[str, Any]) -> int:
    """根据事件等级确定所需救援站数量"""
    base_count = {
        EventLevel.MINOR: 1,
        EventLevel.MAJOR: 2,
        EventLevel.SERIOUS: 3,
        EventLevel.CRITICAL: 4
    }.get(event_level, 1)
    
    # 根据其他因素调整
    if event_context.get("hazmat", False):
        base_count += 1
    
    if event_context.get("trapped_people", 0) > 10:
        base_count += 1
    
    if event_context.get("building_type") == "high_rise":
        base_count += 1
    
    return min(base_count, 5)  # 最多5个站


@tool
async def generate_dispatch_plans(
    event_id: str,
    primary_stations: List[Dict[str, Any]],
    backup_stations: List[Dict[str, Any]],
    nearby_hydrants: List[Dict[str, Any]],
    reasoning: str
) -> List[Dict[str, Any]]:
    """
    生成调度方案
    
    Args:
        event_id: 事件ID
        primary_stations: 主力救援站
        backup_stations: 备用救援站
        nearby_hydrants: 附近消火栓
        reasoning: 决策理由
    
    Returns:
        调度方案列表
    """
    plans = []
    
    if not primary_stations:
        return plans
    
    # 方案1: 标准方案 (主力站点)
    plan1 = create_dispatch_plan(
        event_id=event_id,
        primary_stations=primary_stations,
        backup_stations=[],
        hydrants=nearby_hydrants[:3],  # 选择最近的3个消火栓
        plan_type="standard",
        reasoning=f"标准方案: {reasoning}"
    )
    plans.append(plan1)
    
    # 方案2: 增强方案 (主力 + 部分备用)
    if backup_stations and len(primary_stations) < 3:
        enhanced_primary = primary_stations + backup_stations[:1]
        plan2 = create_dispatch_plan(
            event_id=event_id,
            primary_stations=enhanced_primary,
            backup_stations=backup_stations[1:],
            hydrants=nearby_hydrants[:5],
            plan_type="enhanced",
            reasoning=f"增强方案: 增加备用站点提高响应能力"
        )
        plans.append(plan2)
    
    # 方案3: 最小方案 (仅最优站点)
    if len(primary_stations) > 1:
        plan3 = create_dispatch_plan(
            event_id=event_id,
            primary_stations=primary_stations[:1],
            backup_stations=primary_stations[1:] + backup_stations,
            hydrants=nearby_hydrants[:2],
            plan_type="minimal",
            reasoning=f"最小方案: 优先最近站点快速响应"
        )
        plans.append(plan3)
    
    return plans


def create_dispatch_plan(
    event_id: str,
    primary_stations: List[Dict[str, Any]],
    backup_stations: List[Dict[str, Any]],
    hydrants: List[Dict[str, Any]],
    plan_type: str,
    reasoning: str
) -> Dict[str, Any]:
    """创建调度方案"""
    
    plan_id = f"{event_id}_{plan_type}_{uuid.uuid4().hex[:8]}"
    
    # 转换为 ResourceUnit
    primary_resources = [dict_to_resource_unit(s) for s in primary_stations]
    backup_resources = [dict_to_resource_unit(s) for s in backup_stations]
    hydrant_resources = [dict_to_resource_unit(h) for h in hydrants]
    
    # 计算预计到达时间 (基于最近站点)
    eta_minutes = 10  # 默认10分钟
    if primary_stations:
        min_distance = min(s.get("distance_km", 10) for s in primary_stations)
        # 简单估算: 距离(km) * 2 + 5分钟准备时间
        eta_minutes = int(min_distance * 2 + 5)
    
    # 计算置信度
    confidence = calculate_plan_confidence(primary_stations, backup_stations, hydrants)
    
    # 计算资源充足度
    resource_adequacy = calculate_resource_adequacy(primary_stations, backup_stations)
    
    # 计算覆盖评分
    coverage_score = calculate_coverage_score(primary_stations, hydrants)
    
    return {
        "plan_id": plan_id,
        "event_id": event_id,
        "plan_type": plan_type,
        "primary_stations": primary_resources,
        "backup_stations": backup_resources,
        "hydrants": hydrant_resources,
        "eta_minutes": eta_minutes,
        "confidence": confidence,
        "resource_adequacy": resource_adequacy,
        "coverage_score": coverage_score,
        "reasoning": reasoning,
        "status": DispatchStatus.PENDING.value,
        "created_at": datetime.now().isoformat()
    }


def dict_to_resource_unit(resource_dict: Dict[str, Any]) -> ResourceUnit:
    """将字典转换为 ResourceUnit"""
    from ..models.resource import ResourceCapacity
    
    capacity_data = resource_dict.get("capacity", {})
    capacity = ResourceCapacity(
        personnel_count=capacity_data.get("personnel_count", 0),
        vehicles=capacity_data.get("vehicles", [])
    )
    
    return ResourceUnit(
        id=resource_dict["id"],
        name=resource_dict["name"],
        type=resource_dict["type"],
        location=tuple(resource_dict["location"]),
        dadui=resource_dict.get("dadui", ""),
        status=ResourceStatus.AVAILABLE,
        capacity=capacity,
        additional_info=resource_dict.get("additional_info", {})
    )


def calculate_plan_confidence(primary_stations: List[Dict], backup_stations: List[Dict], hydrants: List[Dict]) -> float:
    """计算方案置信度"""
    confidence = 0.5  # 基础置信度
    
    # 主力站点加分
    if primary_stations:
        confidence += min(len(primary_stations) * 0.15, 0.3)
    
    # 备用站点加分
    if backup_stations:
        confidence += min(len(backup_stations) * 0.1, 0.15)
    
    # 消火栓加分
    if hydrants:
        confidence += min(len(hydrants) * 0.02, 0.05)
    
    return min(confidence, 1.0)


def calculate_resource_adequacy(primary_stations: List[Dict], backup_stations: List[Dict]) -> float:
    """计算资源充足度"""
    total_personnel = 0
    total_vehicles = 0
    
    for station in primary_stations + backup_stations:
        capacity = station.get("capacity", {})
        total_personnel += capacity.get("personnel_count", 0)
        vehicles = capacity.get("vehicles", [])
        total_vehicles += sum(v.get("count", 1) for v in vehicles)
    
    # 基于人员和车辆数量评估充足度
    personnel_adequacy = min(total_personnel / 20, 1.0)  # 20人为充足
    vehicle_adequacy = min(total_vehicles / 3, 1.0)      # 3台车为充足
    
    return (personnel_adequacy + vehicle_adequacy) / 2


def calculate_coverage_score(primary_stations: List[Dict], hydrants: List[Dict]) -> float:
    """计算覆盖评分"""
    coverage = 0.0
    
    # 救援站覆盖
    if primary_stations:
        min_distance = min(s.get("distance_km", float('inf')) for s in primary_stations)
        if min_distance < 5:
            coverage += 0.5
        elif min_distance < 10:
            coverage += 0.3
        else:
            coverage += 0.1
    
    # 消火栓覆盖
    if hydrants:
        min_hydrant_distance = min(h.get("distance_km", float('inf')) for h in hydrants)
        if min_hydrant_distance < 1:
            coverage += 0.5
        elif min_hydrant_distance < 2:
            coverage += 0.3
        else:
            coverage += 0.2
    
    return min(coverage, 1.0)


async def dispatch_planning_node(state: FireDispatchState) -> FireDispatchState:
    """调度规划节点"""
    start_time = time.time()
    
    try:
        state.set_stage(ProcessingStage.DISPATCH_PLANNING)
        state.current_agent = "dispatch_planner"
        
        if not state.spatial_analysis or not state.spatial_analysis.nearby_stations:
            error_msg = "缺少空间分析结果或无可用救援站"
            state.add_error(error_msg)
            state.add_agent_result("dispatch_planner", AgentStatus.FAILED, error=error_msg)
            return state
        
        # 准备数据
        event_context = state.event.__dict__ if state.event else {}
        available_stations = [
            {
                "id": s.id,
                "name": s.name,
                "type": s.type,
                "location": s.location,
                "dadui": s.dadui,
                "status": s.status.value,
                "capacity": {
                    "personnel_count": s.capacity.personnel_count if s.capacity else 0,
                    "vehicles": s.capacity.vehicles if s.capacity else []
                },
                "distance_km": s.additional_info.get("distance_km", 0),
                "additional_info": s.additional_info
            }
            for s in state.spatial_analysis.nearby_stations
        ]
        
        nearby_hydrants = [
            {
                "id": h.id,
                "name": h.name,
                "type": h.type,
                "location": h.location,
                "distance_km": h.additional_info.get("distance_km", 0),
                "additional_info": h.additional_info
            }
            for h in state.spatial_analysis.nearby_hydrants
        ]
        
        # 选择最优资源
        resource_selection = await select_optimal_resources(
            event_context,
            available_stations,
            state.spatial_analysis.jurisdiction_dadui,
            settings.business.max_dispatch_plans
        )
        
        # 生成调度方案
        dispatch_plans_data = await generate_dispatch_plans(
            state.event.event_id,
            resource_selection["primary"],
            resource_selection["backup"],
            nearby_hydrants,
            resource_selection["reasoning"]
        )
        
        # 转换为 DispatchPlan 对象
        dispatch_plans = []
        for plan_data in dispatch_plans_data:
            plan = DispatchPlan(
                plan_id=plan_data["plan_id"],
                event_id=plan_data["event_id"],
                primary_stations=plan_data["primary_stations"],
                backup_stations=plan_data["backup_stations"],
                hydrants=plan_data["hydrants"],
                eta_minutes=plan_data["eta_minutes"],
                confidence=plan_data["confidence"],
                resource_adequacy=plan_data["resource_adequacy"],
                coverage_score=plan_data["coverage_score"],
                reasoning=plan_data["reasoning"]
            )
            dispatch_plans.append(plan)
        
        # 选择推荐方案 (评分最高的)
        selected_plan = None
        if dispatch_plans:
            selected_plan = max(dispatch_plans, key=lambda p: p.get_overall_score())
        
        # 创建调度规划结果
        state.dispatch_planning = DispatchPlanningResult(
            primary_plans=dispatch_plans,
            backup_plans=[],
            selected_plan=selected_plan,
            planning_reasoning=resource_selection["reasoning"],
            confidence_score=selected_plan.confidence if selected_plan else 0.0
        )
        
        # 添加处理消息
        state.messages.append(AIMessage(
            content=f"调度规划完成: 生成{len(dispatch_plans)}个方案, "
                   f"推荐方案ETA {selected_plan.eta_minutes}分钟" if selected_plan else "调度规划完成: 无可用方案"
        ))
        
        # 记录成功结果
        execution_time = time.time() - start_time
        state.add_agent_result(
            "dispatch_planner",
            AgentStatus.COMPLETED,
            result={
                "plans_generated": len(dispatch_plans),
                "selected_plan_id": selected_plan.plan_id if selected_plan else None,
                "eta_minutes": selected_plan.eta_minutes if selected_plan else None,
                "confidence": selected_plan.confidence if selected_plan else 0.0,
                "resource_selection": resource_selection["selection_details"]
            },
            execution_time=execution_time
        )
        
        state.next_agent = "route_evaluator"
        
    except Exception as e:
        error_msg = f"调度规划失败: {str(e)}"
        state.add_error(error_msg)
        state.add_agent_result("dispatch_planner", AgentStatus.FAILED, error=error_msg)
    
    return state


def create_dispatch_planner_agent() -> StateGraph:
    """创建调度规划 Agent"""
    
    # 创建状态图
    workflow = StateGraph(FireDispatchState)
    
    # 添加节点
    workflow.add_node("dispatch_planning", dispatch_planning_node)
    
    # 设置入口点和结束点
    workflow.set_entry_point("dispatch_planning")
    workflow.add_edge("dispatch_planning", END)
    
    return workflow.compile()
