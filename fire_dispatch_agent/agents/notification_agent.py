"""
通知推送 Agent

负责通知推送和状态同步，包括WebSocket推送、外部系统集成等
"""

import time
import json
from typing import Dict, Any, List, Optional

from langgraph.graph import StateGraph, END
from langchain_core.messages import AIMessage
from langchain_core.tools import tool

from ..utils.state import FireDispatchState, AgentStatus, ProcessingStage, NotificationResult
from ..config.settings import settings


@tool
async def send_websocket_notification(
    event_id: str,
    message_type: str,
    data: Dict[str, Any],
    target_clients: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    发送WebSocket通知
    
    Args:
        event_id: 事件ID
        message_type: 消息类型
        data: 消息数据
        target_clients: 目标客户端列表
    
    Returns:
        发送结果
    """
    try:
        from ..utils.websocket_manager import WebSocketManager
        
        ws_manager = WebSocketManager()
        
        message = {
            "type": message_type,
            "event_id": event_id,
            "data": data,
            "timestamp": time.time()
        }
        
        if target_clients:
            # 发送给指定客户端
            sent_count = 0
            for client_id in target_clients:
                success = await ws_manager.send_to_client(client_id, message)
                if success:
                    sent_count += 1
        else:
            # 广播给所有客户端
            sent_count = await ws_manager.broadcast(message)
        
        return {
            "success": True,
            "sent_count": sent_count,
            "message_type": message_type,
            "method": "websocket"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "sent_count": 0,
            "method": "websocket"
        }


@tool
async def notify_external_systems(
    event_id: str,
    dispatch_plan: Dict[str, Any],
    notification_type: str = "dispatch_created"
) -> Dict[str, Any]:
    """
    通知外部系统
    
    Args:
        event_id: 事件ID
        dispatch_plan: 调度方案
        notification_type: 通知类型
    
    Returns:
        通知结果
    """
    results = {}
    
    # 通知指挥中心系统
    try:
        command_center_result = await notify_command_center(event_id, dispatch_plan, notification_type)
        results["command_center"] = command_center_result
    except Exception as e:
        results["command_center"] = {"success": False, "error": str(e)}
    
    # 通知救援站系统
    try:
        stations_result = await notify_rescue_stations(dispatch_plan, notification_type)
        results["rescue_stations"] = stations_result
    except Exception as e:
        results["rescue_stations"] = {"success": False, "error": str(e)}
    
    # 通知交通管理系统
    try:
        traffic_result = await notify_traffic_management(dispatch_plan, notification_type)
        results["traffic_management"] = traffic_result
    except Exception as e:
        results["traffic_management"] = {"success": False, "error": str(e)}
    
    return {
        "notification_type": notification_type,
        "systems_notified": len(results),
        "results": results,
        "overall_success": all(r.get("success", False) for r in results.values())
    }


async def notify_command_center(event_id: str, dispatch_plan: Dict[str, Any], notification_type: str) -> Dict[str, Any]:
    """通知指挥中心"""
    # 模拟通知指挥中心系统
    # 实际实现中应该调用指挥中心的API
    
    notification_data = {
        "event_id": event_id,
        "plan_id": dispatch_plan.get("plan_id"),
        "primary_stations": [s.get("name") for s in dispatch_plan.get("primary_stations", [])],
        "eta_minutes": dispatch_plan.get("eta_minutes"),
        "notification_type": notification_type,
        "timestamp": time.time()
    }
    
    # 模拟API调用
    await asyncio.sleep(0.1)  # 模拟网络延迟
    
    return {
        "success": True,
        "system": "command_center",
        "response_code": 200,
        "message": "指挥中心已收到通知"
    }


async def notify_rescue_stations(dispatch_plan: Dict[str, Any], notification_type: str) -> Dict[str, Any]:
    """通知救援站"""
    stations = dispatch_plan.get("primary_stations", []) + dispatch_plan.get("backup_stations", [])
    
    notifications_sent = []
    for station in stations:
        station_notification = {
            "station_id": station.get("id"),
            "station_name": station.get("name"),
            "role": "primary" if station in dispatch_plan.get("primary_stations", []) else "backup",
            "event_location": dispatch_plan.get("event_location"),
            "eta_minutes": dispatch_plan.get("eta_minutes"),
            "notification_type": notification_type
        }
        
        # 模拟发送通知到救援站
        # 实际实现中应该调用救援站的通信系统
        notifications_sent.append({
            "station_id": station.get("id"),
            "success": True,
            "method": "radio_system"
        })
    
    return {
        "success": True,
        "stations_notified": len(notifications_sent),
        "notifications": notifications_sent
    }


async def notify_traffic_management(dispatch_plan: Dict[str, Any], notification_type: str) -> Dict[str, Any]:
    """通知交通管理系统"""
    routes = dispatch_plan.get("routes", [])
    
    if not routes:
        return {"success": True, "message": "无需交通协调"}
    
    # 提取需要交通协调的路段
    traffic_coordination_requests = []
    for route in routes:
        if route.get("risk_level") in ["medium", "high"] or route.get("traffic_delay_minutes", 0) > 5:
            traffic_coordination_requests.append({
                "route_id": route.get("route_id"),
                "road_names": route.get("road_names", []),
                "priority": "high" if route.get("risk_level") == "high" else "medium",
                "estimated_duration": route.get("duration_minutes", 0)
            })
    
    if traffic_coordination_requests:
        # 模拟通知交通管理系统
        return {
            "success": True,
            "coordination_requests": len(traffic_coordination_requests),
            "requests": traffic_coordination_requests,
            "message": "交通协调请求已发送"
        }
    
    return {"success": True, "message": "无需交通协调"}


@tool
async def generate_dispatch_summary(
    event_context: Dict[str, Any],
    dispatch_plan: Dict[str, Any],
    route_evaluation: Dict[str, Any]
) -> Dict[str, Any]:
    """
    生成调度摘要
    
    Args:
        event_context: 事件上下文
        dispatch_plan: 调度方案
        route_evaluation: 路径评估
    
    Returns:
        调度摘要
    """
    summary = {
        "event_summary": {
            "event_id": event_context.get("event_id"),
            "location": event_context.get("address"),
            "level": event_context.get("level", {}).get("value") if isinstance(event_context.get("level"), dict) else str(event_context.get("level", "")),
            "type": event_context.get("event_type", {}).get("value") if isinstance(event_context.get("event_type"), dict) else str(event_context.get("event_type", "")),
            "priority_score": event_context.get("priority_score", 0)
        },
        "dispatch_summary": {
            "plan_id": dispatch_plan.get("plan_id"),
            "primary_stations": len(dispatch_plan.get("primary_stations", [])),
            "backup_stations": len(dispatch_plan.get("backup_stations", [])),
            "total_personnel": sum(
                s.get("capacity", {}).get("personnel_count", 0) 
                for s in dispatch_plan.get("primary_stations", [])
            ),
            "total_vehicles": sum(
                len(s.get("capacity", {}).get("vehicles", [])) 
                for s in dispatch_plan.get("primary_stations", [])
            ),
            "eta_minutes": dispatch_plan.get("eta_minutes"),
            "confidence": dispatch_plan.get("confidence", 0)
        },
        "route_summary": {
            "total_routes": len(route_evaluation.get("routes", [])),
            "alternative_routes": len(route_evaluation.get("alternative_routes", [])),
            "min_eta": route_evaluation.get("eta_analysis", {}).get("min_eta"),
            "traffic_impact": route_evaluation.get("eta_analysis", {}).get("traffic_impact", 0)
        },
        "recommendations": generate_dispatch_recommendations(event_context, dispatch_plan, route_evaluation)
    }
    
    return summary


def generate_dispatch_recommendations(
    event_context: Dict[str, Any],
    dispatch_plan: Dict[str, Any],
    route_evaluation: Dict[str, Any]
) -> List[str]:
    """生成调度建议"""
    recommendations = []
    
    # 基于事件等级的建议
    event_level = event_context.get("level")
    if event_level and str(event_level).endswith("CRITICAL"):
        recommendations.append("特别重大事件，建议启动一级响应")
        recommendations.append("考虑请求上级支援")
    
    # 基于ETA的建议
    eta_minutes = dispatch_plan.get("eta_minutes", 0)
    if eta_minutes > 15:
        recommendations.append("到达时间较长，建议通知现场人员自救")
        recommendations.append("考虑就近调派其他资源")
    
    # 基于路况的建议
    traffic_impact = route_evaluation.get("eta_analysis", {}).get("traffic_impact", 0)
    if traffic_impact > 5:
        recommendations.append("交通拥堵严重，建议协调交警疏导")
        recommendations.append("启用应急通道")
    
    # 基于资源充足度的建议
    confidence = dispatch_plan.get("confidence", 0)
    if confidence < 0.7:
        recommendations.append("资源配置置信度较低，建议增加备用方案")
    
    # 基于危险因素的建议
    if event_context.get("hazmat", False):
        recommendations.append("涉及危化品，通知专业处置队伍")
        recommendations.append("设置安全警戒区域")
    
    if event_context.get("trapped_people", 0) > 0:
        recommendations.append("有人员被困，优先救人")
        recommendations.append("准备医疗救护资源")
    
    return recommendations


async def notification_node(state: FireDispatchState) -> FireDispatchState:
    """通知推送节点"""
    start_time = time.time()
    
    try:
        state.set_stage(ProcessingStage.NOTIFICATION)
        state.current_agent = "notification_agent"
        
        if not state.dispatch_planning or not state.dispatch_planning.selected_plan:
            error_msg = "缺少调度方案"
            state.add_error(error_msg)
            state.add_agent_result("notification_agent", AgentStatus.FAILED, error=error_msg)
            return state
        
        selected_plan = state.dispatch_planning.selected_plan
        event_context = state.event.__dict__ if state.event else {}
        route_evaluation = state.route_evaluation.__dict__ if state.route_evaluation else {}
        
        # 生成调度摘要
        dispatch_summary = await generate_dispatch_summary(
            event_context,
            selected_plan.__dict__,
            route_evaluation
        )
        
        # 发送WebSocket通知
        websocket_result = await send_websocket_notification(
            state.event.event_id,
            "dispatch_plan_ready",
            {
                "plan": selected_plan.__dict__,
                "summary": dispatch_summary,
                "routes": [r.__dict__ for r in (state.route_evaluation.routes if state.route_evaluation else [])]
            }
        )
        
        # 通知外部系统
        external_systems_result = await notify_external_systems(
            state.event.event_id,
            selected_plan.__dict__,
            "dispatch_created"
        )
        
        # 创建通知结果
        state.notification = NotificationResult(
            notifications_sent=[
                {
                    "type": "websocket",
                    "success": websocket_result["success"],
                    "details": websocket_result
                },
                {
                    "type": "external_systems",
                    "success": external_systems_result["overall_success"],
                    "details": external_systems_result
                }
            ],
            websocket_clients=[f"client_{i}" for i in range(websocket_result.get("sent_count", 0))],
            external_systems=list(external_systems_result.get("results", {}).keys()),
            notification_status={
                "websocket": "success" if websocket_result["success"] else "failed",
                "external_systems": "success" if external_systems_result["overall_success"] else "partial"
            }
        )
        
        # 设置最终调度方案
        state.final_dispatch_plan = selected_plan
        
        # 设置执行摘要
        state.execution_summary = dispatch_summary
        
        # 添加处理消息
        state.messages.append(AIMessage(
            content=f"通知推送完成: WebSocket {websocket_result.get('sent_count', 0)}个客户端, "
                   f"外部系统 {len(external_systems_result.get('results', {}))}个"
        ))
        
        # 记录成功结果
        execution_time = time.time() - start_time
        state.add_agent_result(
            "notification_agent",
            AgentStatus.COMPLETED,
            result={
                "websocket_clients": websocket_result.get("sent_count", 0),
                "external_systems": len(external_systems_result.get("results", {})),
                "notifications_sent": len(state.notification.notifications_sent),
                "overall_success": all(n["success"] for n in state.notification.notifications_sent)
            },
            execution_time=execution_time
        )
        
        # 设置处理完成
        state.set_stage(ProcessingStage.COMPLETED)
        
    except Exception as e:
        error_msg = f"通知推送失败: {str(e)}"
        state.add_error(error_msg)
        state.add_agent_result("notification_agent", AgentStatus.FAILED, error=error_msg)
    
    return state


def create_notification_agent() -> StateGraph:
    """创建通知推送 Agent"""
    
    # 创建状态图
    workflow = StateGraph(FireDispatchState)
    
    # 添加节点
    workflow.add_node("notification", notification_node)
    
    # 设置入口点和结束点
    workflow.set_entry_point("notification")
    workflow.add_edge("notification", END)
    
    return workflow.compile()
