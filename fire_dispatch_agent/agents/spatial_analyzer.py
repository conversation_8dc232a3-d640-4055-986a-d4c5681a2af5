"""
空间分析 Agent

负责空间查询和分析，包括辖区判断、附近资源查询、覆盖分析等
"""

import time
from typing import Dict, Any, List, Tuple, Optional

from langgraph.graph import StateGraph, END
from langchain_core.messages import AIMessage
from langchain_core.tools import tool

from ..utils.state import FireDispatchState, AgentStatus, ProcessingStage, SpatialAnalysisResult
from ..models.resource import ResourceUnit, StationType, ResourceStatus, ResourceCapacity
from ..utils.geo_utils import calculate_distance, point_in_polygon, parse_geojson_polygon, parse_wkt_polygon
from ..config.settings import settings


@tool
async def query_nearby_stations(longitude: float, latitude: float, radius_km: float = 10) -> List[Dict[str, Any]]:
    """
    查询附近的救援站
    
    Args:
        longitude: 经度
        latitude: 纬度
        radius_km: 搜索半径 (公里)
    
    Returns:
        救援站列表
    """
    try:
        from ..utils.database import DatabaseClient
        db_client = DatabaseClient()
        
        # 基于你的数据结构查询
        query = """
        SELECT 
            rank as id,
            dadui,
            jyz as name,
            CAST(horizontal AS FLOAT) as lng,
            CAST(vertical AS FLOAT) as lat,
            type,
            content
        FROM report_map.jyz_coordinate
        WHERE horizontal IS NOT NULL AND vertical IS NOT NULL
        ORDER BY (
            (CAST(horizontal AS FLOAT) - %s) * (CAST(horizontal AS FLOAT) - %s) + 
            (CAST(vertical AS FLOAT) - %s) * (CAST(vertical AS FLOAT) - %s)
        ) ASC
        LIMIT 20;
        """
        
        results = await db_client.execute_query(query, (longitude, longitude, latitude, latitude))
        
        stations = []
        for row in results:
            # 计算实际距离
            distance = calculate_distance((longitude, latitude), (row["lng"], row["lat"]))
            
            if distance <= radius_km:
                # 解析 content 字段获取详细信息
                content = row.get("content", "")
                capacity = parse_station_content(content)
                
                stations.append({
                    "id": str(row["id"]),
                    "name": row["name"],
                    "dadui": row["dadui"],
                    "location": (row["lng"], row["lat"]),
                    "type": "rescue_station" if row["type"] == 1 else "fulltime_station" if row["type"] == 2 else "logistics_station",
                    "distance_km": round(distance, 2),
                    "capacity": capacity,
                    "status": "available"  # 默认可用，实际应查询实时状态
                })
        
        return stations
        
    except Exception as e:
        # 降级处理：返回模拟数据
        return [
            {
                "id": "18",
                "name": "北京街消防救援站",
                "dadui": "西岗大队",
                "location": (121.612341, 38.917218),
                "type": "rescue_station",
                "distance_km": calculate_distance((longitude, latitude), (121.612341, 38.917218)),
                "capacity": {
                    "personnel_count": 26,
                    "vehicles": [{"type": "消防车", "count": 6}],
                    "description": "地址：大连市西岗区北京街57号,值班电话：0411-83632330"
                },
                "status": "available"
            }
        ]


@tool
async def query_nearby_hydrants(longitude: float, latitude: float, radius_km: float = 5) -> List[Dict[str, Any]]:
    """
    查询附近的消火栓
    
    Args:
        longitude: 经度
        latitude: 纬度
        radius_km: 搜索半径 (公里)
    
    Returns:
        消火栓列表
    """
    try:
        from ..utils.database import DatabaseClient
        db_client = DatabaseClient()
        
        # 使用你提供的SQL查询
        query = """
        SELECT
            combat_training_data.rep_watersource.lon_correct as lng,
            combat_training_data.rep_watersource.lat_correct as lat,
            combat_training_data.rep_watersource.NAME as name,
            rep_watersource."type" as hydrant_type
        FROM combat_training_data.rep_watersource 
        WHERE rep_watersource."type" IN ('消火栓','消防水鹤')
        AND lon_correct IS NOT NULL AND lat_correct IS NOT NULL
        ORDER BY (
            (lon_correct - %s) * (lon_correct - %s) + 
            (lat_correct - %s) * (lat_correct - %s)
        ) ASC
        LIMIT 50;
        """
        
        results = await db_client.execute_query(query, (longitude, longitude, latitude, latitude))
        
        hydrants = []
        for i, row in enumerate(results):
            # 计算实际距离
            distance = calculate_distance((longitude, latitude), (row["lng"], row["lat"]))
            
            if distance <= radius_km:
                hydrants.append({
                    "id": f"hydrant_{i}",
                    "name": row["name"],
                    "location": (row["lng"], row["lat"]),
                    "type": "hydrant",
                    "hydrant_type": row["hydrant_type"],
                    "style": 3 if row["hydrant_type"] == "消火栓" else 4,
                    "distance_km": round(distance, 2),
                    "status": "available"
                })
        
        return hydrants
        
    except Exception as e:
        # 降级处理：返回模拟数据
        return [
            {
                "id": "hydrant_1",
                "name": "北京街消火栓001",
                "location": (121.615, 38.920),
                "type": "hydrant",
                "hydrant_type": "消火栓",
                "style": 3,
                "distance_km": calculate_distance((longitude, latitude), (121.615, 38.920)),
                "status": "available"
            }
        ]


@tool
async def determine_jurisdiction(longitude: float, latitude: float) -> Dict[str, Any]:
    """
    判断事件所属辖区大队
    
    Args:
        longitude: 经度
        latitude: 纬度
    
    Returns:
        辖区信息
    """
    try:
        from ..utils.database import DatabaseClient
        db_client = DatabaseClient()
        
        # 查询大队辖区边界
        query = """
        SELECT dadui, geojson, center
        FROM report_map.dadui_geojson
        ORDER BY rank;
        """
        
        results = await db_client.execute_query(query)
        
        point = (longitude, latitude)
        
        for row in results:
            dadui = row["dadui"]
            geojson_str = row["geojson"]
            
            # 解析边界数据
            polygon = None
            if geojson_str:
                # 尝试解析 GeoJSON
                polygon = parse_geojson_polygon(geojson_str)
                
                if not polygon:
                    # 尝试解析 WKT
                    polygon = parse_wkt_polygon(geojson_str)
            
            # 点在多边形内判断
            if polygon and point_in_polygon(point, polygon):
                return {
                    "dadui": dadui,
                    "confidence": 0.95,
                    "method": "polygon_intersection"
                }
        
        # 如果没有找到，使用最近救援站的大队
        nearby_stations = await query_nearby_stations(longitude, latitude, 50)
        if nearby_stations:
            return {
                "dadui": nearby_stations[0]["dadui"],
                "confidence": 0.7,
                "method": "nearest_station"
            }
        
        return {
            "dadui": "未知大队",
            "confidence": 0.0,
            "method": "default"
        }
        
    except Exception as e:
        # 降级处理：基于坐标范围判断
        if 121.5 <= longitude <= 121.7 and 38.8 <= latitude <= 39.0:
            return {
                "dadui": "西岗大队",
                "confidence": 0.5,
                "method": "coordinate_range"
            }
        
        return {
            "dadui": "未知大队",
            "confidence": 0.0,
            "method": "fallback"
        }


def parse_station_content(content: str) -> Dict[str, Any]:
    """解析救援站详细信息"""
    capacity = {
        "personnel_count": 0,
        "vehicles": [],
        "description": content
    }
    
    if not content:
        return capacity
    
    # 解析人员数量
    if "执勤人数：" in content:
        try:
            start = content.find("执勤人数：") + 5
            end = content.find("人", start)
            if end > start:
                personnel_str = content[start:end]
                capacity["personnel_count"] = int(personnel_str)
        except (ValueError, IndexError):
            pass
    
    # 解析车辆数量
    if "执勤车辆：" in content:
        try:
            start = content.find("执勤车辆：") + 5
            end = content.find("台", start)
            if end > start:
                vehicle_str = content[start:end]
                vehicle_count = int(vehicle_str)
                capacity["vehicles"] = [{"type": "消防车", "count": vehicle_count}]
        except (ValueError, IndexError):
            pass
    
    return capacity


async def spatial_analysis_node(state: FireDispatchState) -> FireDispatchState:
    """空间分析节点"""
    start_time = time.time()
    
    try:
        state.set_stage(ProcessingStage.SPATIAL_ANALYSIS)
        state.current_agent = "spatial_analyzer"
        
        if not state.event or not state.event.location:
            error_msg = "缺少事件位置信息"
            state.add_error(error_msg)
            state.add_agent_result("spatial_analyzer", AgentStatus.FAILED, error=error_msg)
            return state
        
        lng, lat = state.event.location
        search_radius = settings.business.default_search_radius
        
        # 判断辖区
        jurisdiction_result = await determine_jurisdiction(lng, lat)
        jurisdiction_dadui = jurisdiction_result["dadui"]
        
        if jurisdiction_result["confidence"] < 0.8:
            state.add_warning(f"辖区判断置信度较低: {jurisdiction_result['confidence']}")
        
        # 查询附近救援站
        stations_data = await query_nearby_stations(lng, lat, search_radius)
        nearby_stations = []
        
        for station_data in stations_data:
            capacity = ResourceCapacity(
                personnel_count=station_data["capacity"].get("personnel_count", 0),
                vehicles=station_data["capacity"].get("vehicles", [])
            )
            
            station = ResourceUnit(
                id=station_data["id"],
                name=station_data["name"],
                type=station_data["type"],
                location=tuple(station_data["location"]),
                dadui=station_data["dadui"],
                status=ResourceStatus.AVAILABLE,
                capacity=capacity,
                additional_info={
                    "distance_km": station_data["distance_km"],
                    "description": station_data["capacity"].get("description", "")
                }
            )
            nearby_stations.append(station)
        
        # 查询附近消火栓
        hydrants_data = await query_nearby_hydrants(lng, lat, search_radius)
        nearby_hydrants = []
        
        for hydrant_data in hydrants_data:
            hydrant = ResourceUnit(
                id=hydrant_data["id"],
                name=hydrant_data["name"],
                type="hydrant",
                location=tuple(hydrant_data["location"]),
                dadui="",
                status=ResourceStatus.AVAILABLE,
                additional_info={
                    "distance_km": hydrant_data["distance_km"],
                    "hydrant_type": hydrant_data["hydrant_type"],
                    "style": hydrant_data["style"]
                }
            )
            nearby_hydrants.append(hydrant)
        
        # 创建空间分析结果
        state.spatial_analysis = SpatialAnalysisResult(
            jurisdiction_dadui=jurisdiction_dadui,
            nearby_stations=nearby_stations,
            nearby_hydrants=nearby_hydrants,
            search_radius_used=search_radius,
            coverage_analysis={
                "total_stations": len(nearby_stations),
                "jurisdiction_stations": len([s for s in nearby_stations if s.dadui == jurisdiction_dadui]),
                "total_hydrants": len(nearby_hydrants),
                "min_station_distance": min([s.additional_info["distance_km"] for s in nearby_stations]) if nearby_stations else float('inf'),
                "min_hydrant_distance": min([h.additional_info["distance_km"] for h in nearby_hydrants]) if nearby_hydrants else float('inf')
            }
        )
        
        # 添加处理消息
        state.messages.append(AIMessage(
            content=f"空间分析完成: 辖区{jurisdiction_dadui}, "
                   f"附近{len(nearby_stations)}个救援站, "
                   f"{len(nearby_hydrants)}个消火栓"
        ))
        
        # 记录成功结果
        execution_time = time.time() - start_time
        state.add_agent_result(
            "spatial_analyzer",
            AgentStatus.COMPLETED,
            result={
                "jurisdiction": jurisdiction_dadui,
                "stations_found": len(nearby_stations),
                "hydrants_found": len(nearby_hydrants),
                "search_radius": search_radius,
                "coverage_analysis": state.spatial_analysis.coverage_analysis
            },
            execution_time=execution_time
        )
        
        state.next_agent = "dispatch_planner"
        
    except Exception as e:
        error_msg = f"空间分析失败: {str(e)}"
        state.add_error(error_msg)
        state.add_agent_result("spatial_analyzer", AgentStatus.FAILED, error=error_msg)
    
    return state


def create_spatial_analyzer_agent() -> StateGraph:
    """创建空间分析 Agent"""
    
    # 创建状态图
    workflow = StateGraph(FireDispatchState)
    
    # 添加节点
    workflow.add_node("spatial_analysis", spatial_analysis_node)
    
    # 设置入口点和结束点
    workflow.set_entry_point("spatial_analysis")
    workflow.add_edge("spatial_analysis", END)
    
    return workflow.compile()
