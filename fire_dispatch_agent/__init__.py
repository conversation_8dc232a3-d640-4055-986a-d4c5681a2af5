"""
灭火指挥调度地图 Agent

基于 LangGraph Supervisor 模式的智能消防救援调度系统
支持多 Agent 协作、实时路径规划、资源优化配置等功能
"""

__version__ = "0.1.0"
__author__ = "Fire Dispatch Team"
__email__ = "<EMAIL>"

from .config.settings import settings
from .models.event import EventContext, EventLevel
from .models.resource import ResourceUnit, StationType
from .models.dispatch import DispatchPlan
from .utils.state import FireDispatchState

__all__ = [
    "settings",
    "EventContext", 
    "EventLevel",
    "ResourceUnit",
    "StationType", 
    "DispatchPlan",
    "FireDispatchState",
]
