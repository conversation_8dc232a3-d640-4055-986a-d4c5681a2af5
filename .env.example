# =============================================================================
# 灭火指挥调度地图 Agent - 环境变量配置示例
# =============================================================================

# -----------------------------------------------------------------------------
# 应用基础配置
# -----------------------------------------------------------------------------
APP_NAME="Fire Dispatch Agent"
APP_VERSION="0.1.0"
DEBUG=false
LOG_LEVEL="INFO"
SECRET_KEY="your-secret-key-here-change-in-production"

# -----------------------------------------------------------------------------
# 数据库配置
# -----------------------------------------------------------------------------
# PostgreSQL 主数据库
DATABASE_URL="************************************************************/integrated_service_governance"
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# 数据库连接重试配置
DB_RETRY_ATTEMPTS=3
DB_RETRY_DELAY=1

# -----------------------------------------------------------------------------
# Redis 缓存配置
# -----------------------------------------------------------------------------
REDIS_URL="redis://localhost:6379/0"
REDIS_PASSWORD=""
REDIS_DB=0
REDIS_MAX_CONNECTIONS=10
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5

# 缓存 TTL 配置 (秒)
CACHE_DEFAULT_TTL=300
CACHE_ETA_TTL=300
CACHE_RESOURCE_STATUS_TTL=60
CACHE_DISPATCH_PLAN_TTL=1800

# -----------------------------------------------------------------------------
# LLM 配置
# -----------------------------------------------------------------------------
# OpenAI 配置
OPENAI_API_KEY="sk-your-openai-api-key-here"
OPENAI_MODEL="gpt-4o"
OPENAI_TEMPERATURE=0.1
OPENAI_MAX_TOKENS=2000
OPENAI_TIMEOUT=30

# LangGraph 配置
LANGGRAPH_API_URL="https://api.langgraph.com"
LANGGRAPH_API_KEY="your-langgraph-api-key"

# -----------------------------------------------------------------------------
# 高德地图 API 配置
# -----------------------------------------------------------------------------
AMAP_API_KEY="your-amap-api-key-here"
AMAP_BASE_URL="https://restapi.amap.com"
AMAP_TIMEOUT=10
AMAP_RETRY_ATTEMPTS=3

# 高德地图服务配置
AMAP_GEOCODING_URL="/v3/geocode/geo"
AMAP_REGEO_URL="/v3/geocode/regeo"
AMAP_DIRECTION_URL="/v3/direction/driving"
AMAP_TRAFFIC_URL="/v3/traffic/status/rectangle"

# -----------------------------------------------------------------------------
# 业务配置
# -----------------------------------------------------------------------------
# 搜索配置
DEFAULT_SEARCH_RADIUS=10.0
MAX_SEARCH_RADIUS=50.0
MAX_NEARBY_STATIONS=20
MAX_NEARBY_HYDRANTS=50

# 调度配置
MAX_DISPATCH_PLANS=3
MIN_DISPATCH_PLANS=1
ENABLE_BACKUP_PLANS=true
JURISDICTION_PRIORITY=true

# 路径规划配置
ENABLE_TRAFFIC_CONSIDERATION=true
ROUTE_ALTERNATIVES=3
MAX_ROUTE_DISTANCE=100.0

# 通知配置
ENABLE_WEBSOCKET_NOTIFICATIONS=true
WEBSOCKET_HEARTBEAT_INTERVAL=30
MAX_WEBSOCKET_CONNECTIONS=1000

# -----------------------------------------------------------------------------
# API 服务配置
# -----------------------------------------------------------------------------
# FastAPI 配置
API_HOST="0.0.0.0"
API_PORT=8000
API_WORKERS=4
API_RELOAD=false

# CORS 配置
CORS_ORIGINS="http://localhost:3000,http://localhost:8080"
CORS_CREDENTIALS=true
CORS_METHODS="GET,POST,PUT,DELETE,OPTIONS"
CORS_HEADERS="*"

# 限流配置
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# -----------------------------------------------------------------------------
# 监控与日志配置
# -----------------------------------------------------------------------------
# 日志配置
LOG_FORMAT="json"
LOG_FILE_PATH="./logs/fire_dispatch.log"
LOG_MAX_SIZE="100MB"
LOG_BACKUP_COUNT=5

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# Sentry 错误监控 (可选)
SENTRY_DSN=""
SENTRY_ENVIRONMENT="development"
SENTRY_TRACES_SAMPLE_RATE=0.1

# -----------------------------------------------------------------------------
# 安全配置
# -----------------------------------------------------------------------------
# JWT 配置
JWT_SECRET_KEY="your-jwt-secret-key-here"
JWT_ALGORITHM="HS256"
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# API 密钥配置
API_KEY_HEADER="X-API-Key"
ADMIN_API_KEY="your-admin-api-key-here"

# -----------------------------------------------------------------------------
# 开发与测试配置
# -----------------------------------------------------------------------------
# 测试数据库
TEST_DATABASE_URL="postgresql://test_user:test_pass@localhost:5432/fire_dispatch_test"

# 开发模式配置
DEV_MODE=false
MOCK_EXTERNAL_APIS=false
ENABLE_API_DOCS=true
ENABLE_PROFILING=false

# -----------------------------------------------------------------------------
# Docker 配置
# -----------------------------------------------------------------------------
DOCKER_REGISTRY="your-registry.com"
DOCKER_IMAGE_TAG="latest"
DOCKER_NETWORK="fire-dispatch-network"

# -----------------------------------------------------------------------------
# 部署配置
# -----------------------------------------------------------------------------
DEPLOYMENT_ENVIRONMENT="development"
KUBERNETES_NAMESPACE="fire-dispatch"
REPLICA_COUNT=1

# 健康检查配置
HEALTH_CHECK_PATH="/health"
READINESS_CHECK_PATH="/ready"
LIVENESS_CHECK_PATH="/live"

# -----------------------------------------------------------------------------
# 特性开关
# -----------------------------------------------------------------------------
FEATURE_ADVANCED_ROUTING=true
FEATURE_MULTI_LANGUAGE=false
FEATURE_REAL_TIME_TRACKING=true
FEATURE_HISTORICAL_ANALYSIS=false
FEATURE_PREDICTIVE_DISPATCH=false
